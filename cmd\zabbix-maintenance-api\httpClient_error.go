package main

import (
	"fmt"
)

type errBadStatusCode struct {
	Code   int
	Status string
}

func (e errBadStatusCode) Error() string {
	if e.Status != "" {
		return fmt.Sprintf("Got a bad status code %s", e.Status)
	} else if e.Code != 0 {
		return fmt.Sprintf("Got a bad status code %d", e.Code)
	}
	return "Got a bad status code"

}

func isBadStatusCodeError(e error) bool {
	_, ok := e.(errBadStatusCode)
	return ok
}

type errDryRun struct {
	ZabbixMethod string
}

func (e errDryRun) Error() string {
	if e.ZabbixMethod != "" {
		return fmt.Sprintf("Zabbix request of the method %s will not be handled when running in dryrun", e.ZabbixMethod)
	}
	return "This is a dryrun, data will not be changed"

}

func isDryRunError(e error) bool {
	_, ok := e.(errDryRun)
	return ok
}
