package main

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestExtractHostIDsOK(t *testing.T) {
	input := []interface{}{
		map[string]interface{}{
			"name":   "test",
			"hostid": "123",
		},
	}

	output := extractHostIDs(input)

	assert.NotEmpty(t, output)
	assert.Equal(t, 1, len(output))

	val, ok := output["test"]

	assert.True(t, ok)
	assert.Equal(t, 123, val)
}

func TestExtractHostIDsBadData(t *testing.T) {
	input := []interface{}{
		map[string]interface{}{
			"name":   "test",
			"hostid": 123, // int is not expected so it will not be added
		},
	}

	output := extractHostIDs(input)

	assert.Empty(t, output)
}
