package main

import (
	"fmt"
)

type errSetValue struct {
	FieldName      string
	FieldType      string
	Err            error
	UnexpectedType bool
}

func (e errSetValue) Error() string {
	if e.UnexpectedType == true {
		return fmt.Sprintf("unable to set value on field %s as type %s was unexpected", e.<PERSON>, e.FieldType)
	}

	if e.Err != nil {
		return fmt.Sprintf("error when setting value on field %s: %s", e.<PERSON><PERSON><PERSON>, e.Err.<PERSON><PERSON>r())
	}

	return fmt.Sprintf("unable to set value on field %s", e.<PERSON><PERSON><PERSON>)
}
