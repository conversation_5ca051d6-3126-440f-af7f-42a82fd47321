package main

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIsTokenAutenticationHTTPError(t *testing.T) {
	_httpClient = newTestClient(func(req *http.Request) *http.Response {
		return &http.Response{
			StatusCode: 500,
			Body:       io.NopCloser(bytes.NewBufferString(`NOT OK`)),
			Header:     make(http.Header),
		}
	})

	assert.False(t, isTokenValid("test"))
}

func TestIsTokenAutenticationOK(t *testing.T) {
	_httpClient = newTestClient(func(req *http.Request) *http.Response {
		r := zabbixResponse{
			JSONRPC: "2.0",
			ID:      1,
			Result: map[string]interface{}{
				"sessionid": "test",
			},
		}
		b, _ := json.Marshal(r)
		return &http.Response{
			StatusCode: 200,
			Body:       io.NopCloser(bytes.NewBuffer(b)),
			Header:     make(http.Header),
		}
	})

	assert.True(t, isTokenValid("test"))
}

func TestIsTokenAutenticationBadTokenResponse(t *testing.T) {
	_httpClient = newTestClient(func(req *http.Request) *http.Response {
		r := zabbixResponse{
			JSONRPC: "2.0",
			ID:      1,
			Result:  1,
		}
		b, _ := json.Marshal(r)
		return &http.Response{
			StatusCode: 200,
			Body:       io.NopCloser(bytes.NewBuffer(b)),
			Header:     make(http.Header),
		}
	})

	assert.False(t, isTokenValid("test"))
}

func TestIsTokenAutenticationZabbixError(t *testing.T) {
	_httpClient = newTestClient(func(req *http.Request) *http.Response {
		r := zabbixResponse{
			JSONRPC: "2.0",
			ID:      1,
			Error: zabbixError{
				Code:    2,
				Message: "Test error",
				Data:    "Test error 2",
			},
		}
		b, _ := json.Marshal(r)
		return &http.Response{
			StatusCode: 200,
			Body:       io.NopCloser(bytes.NewBuffer(b)),
			Header:     make(http.Header),
		}
	})

	assert.False(t, isTokenValid("test"))
}
