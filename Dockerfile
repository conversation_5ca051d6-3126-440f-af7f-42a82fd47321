FROM golang:1.23-alpine3.20 AS build
RUN apk --no-cache --update add make git
WORKDIR /build
COPY . .
RUN go mod download
RUN make build

FROM alpine:3.20
RUN mkdir -p /var/log/zabbix-maintenance-api/
WORKDIR /app
COPY --from=build /build/bin/ .
COPY --from=build /build/mappings.yaml mappings.yaml

CMD /app/zabbix-maintenance-api -cfg /app/config.cfg -cliloglevel debug -tracelog -logfile /var/log/zabbix-maintenance-api/full.log -fileloglevel debug
