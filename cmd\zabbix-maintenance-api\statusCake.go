package main

import (
	"context"
	"sync"
	"time"

	"github.com/StatusCakeDev/statuscake-go"
	"github.com/StatusCakeDev/statuscake-go/credentials"
)

var _statusCakeClient *statuscake.Client

// used by initStatusCakeClient
var _statusCakeClientLock sync.Mutex

func initStatusCakeClient() {
	log.Trace().Msg("Entered function initStatusCakeClient")

	// If the statuscake client has already been initialized we don't need
	// to initialize it once more
	if _statusCakeClient != nil {
		log.Trace().Msg("StatusCake Client: Client already initialized")
		return
	}

	// Aquire the http client lock to ensure we only initialize one
	// instance of the http client
	log.Trace().Msg("StatusCake Client: Requesting StatusCake Client Lock")
	_statusCakeClientLock.Lock()

	// ensure that the lock is released after we have used it
	log.Trace().Msg("StatusCake Client: Defering HTTP Client Unlock")
	defer _statusCakeClientLock.Unlock()
	defer log.Trace().Msg("StatusCake Client: StatusCake Client Lock released")

	log.Trace().Msg("StatusCake Client: StatusCake Client Lock aquired")

	// The http client might have been initialized while
	// we waited for the lock, so we need to check aging
	if _statusCakeClient != nil {
		log.Trace().Msg("StatusCake Client: Client already initialized")
		return
	}

	log.Debug().Msg("StatusCake Client: Initializing StatusCake Client")
	bearer := credentials.NewBearerWithStaticToken(_cfg.StatusCakeAPIToken)
	_statusCakeClient = statuscake.NewClient(statuscake.WithRequestCredentials(bearer), statuscake.WithUserAgent("Scalepoint A/S maintence API"))
}

func DeleteStausCakeMaintenceWindow(id string) error {
	log.Trace().Msg("Entered function DeleteStausCakeMaintenceWindow")

	slog := log.With().Str("ID", id).Logger()
	if _dryRun {
		slog.Trace().Msg("Delete Statuscake Maintenance Window: Skipping deletion of maintenance window as we are running in dryrun mode")
		return nil
	}

	slog.Trace().Msg("Delete Statuscake Maintenance Window: Requesting statuscake client")
	initStatusCakeClient()

	slog.Trace().Msg("Delete Statuscake Maintenance Window: Sending deletion request to statuscake")
	err := _statusCakeClient.DeleteMaintenanceWindow(context.Background(), id).Execute()
	return err
}

func ListStausCakeMaintenceWindow() ([]statuscake.MaintenanceWindow, error) {
	log.Trace().Msg("Entered function ListStausCakeMaintenceWindow")

	log.Trace().Msg("List Statuscake Maintenance Window: Requesting statuscake client")
	initStatusCakeClient()

	log.Trace().Msg("List Statuscake Maintenance Window: Sending request to statuscake")
	res, err := _statusCakeClient.ListMaintenanceWindows(context.Background()).Execute()

	return res.Data, err
}

func CreateStausCakeMaintenceWindow(tags []string, name string) (string, error) {
	log.Trace().Msg("Entered function CreateStausCakeMaintenceWindow")
	slog := log.With().Strs("Tags", tags).Str("Name", name).Logger()

	if _dryRun {
		slog.Trace().Msg("Create Statuscake Maintenance Window: Skipping creation of maintenance window as we are running in dryrun mode")
		return "dryrun", nil
	}

	log.Trace().Msg("Create Statuscake Maintenance Window: Requesting statuscake client")
	initStatusCakeClient()

	start := time.Now().Add(time.Second * 10)
	end := start.Add(time.Second * time.Duration(_cfg.ZabbixDowntime))

	//TODO: time option?
	log.Trace().Msg("Create Statuscake Maintenance Window: Sending request to statuscake")
	res, err := _statusCakeClient.CreateMaintenanceWindow(context.Background()).
		Name(name).
		End(end.UTC()).
		Start(start.UTC()).
		Tags(tags).
		Timezone("UTC").
		Execute()

	return res.Data.NewID, err
}
