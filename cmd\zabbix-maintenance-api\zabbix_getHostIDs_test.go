package main

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetHostIDsHTTPError(t *testing.T) {
	var expectedError errBadStatusCode
	_httpClient = newTestClient(func(req *http.Request) *http.Response {
		return &http.Response{
			StatusCode: 500,
			Body:       io.NopCloser(bytes.NewBufferString(`NOT OK`)),
			Header:     make(http.Header),
		}
	})

	ids, err := getHostIDs("" /*We don't need a token as we intercept the call*/, []string{} /* servers don't matter when testing bad response*/)

	assert.Error(t, err)
	assert.ErrorAs(t, err, &expectedError)
	assert.Empty(t, ids)
}

func TestGetHostIDsOK(t *testing.T) {
	_httpClient = newTestClient(func(req *http.Request) *http.Response {
		r := zabbixResponse{
			JSONRPC: "2.0",
			ID:      1,
			Result: []map[string]interface{}{
				map[string]interface{}{
					"name":   "test",
					"hostid": "123",
				},
			},
		}
		b, _ := json.Marshal(r)
		return &http.Response{
			StatusCode: 200,
			Body:       io.NopCloser(bytes.NewBuffer(b)),
			Header:     make(http.Header),
		}
	})

	ids, err := getHostIDs("" /*We don't need a token as we intercept the call*/, []string{"test"})

	assert.NoError(t, err)
	assert.NotEmpty(t, ids)
}

func TestGetHostIDsMissingServer(t *testing.T) {
	var expectedError errMissingServers
	_httpClient = newTestClient(func(req *http.Request) *http.Response {
		r := zabbixResponse{
			JSONRPC: "2.0",
			ID:      1,
			Result: []map[string]interface{}{
				map[string]interface{}{
					"name":   "missing",
					"hostid": "123",
				},
			},
		}
		b, _ := json.Marshal(r)
		return &http.Response{
			StatusCode: 200,
			Body:       io.NopCloser(bytes.NewBuffer(b)),
			Header:     make(http.Header),
		}
	})

	ids, err := getHostIDs("" /*We don't need a token as we intercept the call*/, []string{"test"})

	assert.Error(t, err)
	assert.ErrorAs(t, err, &expectedError)
	assert.Empty(t, ids)

}

func TestGetHostIDsBadMapResponse(t *testing.T) {
	var expectedError zabbixError
	_httpClient = newTestClient(func(req *http.Request) *http.Response {
		r := zabbixResponse{
			JSONRPC: "2.0",
			ID:      1,
			Result:  1,
		}
		b, _ := json.Marshal(r)
		return &http.Response{
			StatusCode: 200,
			Body:       io.NopCloser(bytes.NewBuffer(b)),
			Header:     make(http.Header),
		}
	})

	ids, err := getHostIDs("" /*We don't need a token as we intercept the call*/, []string{} /* servers don't matter when testing bad response*/)

	assert.Error(t, err)
	assert.ErrorAs(t, err, &expectedError)
	assert.Empty(t, ids)
}

func TestGetHostIDsZabbixError(t *testing.T) {
	var expectedError zabbixError
	_httpClient = newTestClient(func(req *http.Request) *http.Response {
		r := zabbixResponse{
			JSONRPC: "2.0",
			ID:      1,
			Error: zabbixError{
				Code:    2,
				Message: "Test error",
				Data:    "Test error 2",
			},
		}
		b, _ := json.Marshal(r)
		return &http.Response{
			StatusCode: 200,
			Body:       io.NopCloser(bytes.NewBuffer(b)),
			Header:     make(http.Header),
		}
	})

	ids, err := getHostIDs("" /*We don't need a token as we intercept the call*/, []string{} /* servers don't matter when testing bad response*/)

	assert.Error(t, err)
	assert.ErrorAs(t, err, &expectedError)
	assert.Empty(t, ids)
}
