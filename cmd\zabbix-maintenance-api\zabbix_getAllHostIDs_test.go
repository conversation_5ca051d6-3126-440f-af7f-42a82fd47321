package main

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetAllHostIDsHTTPError(t *testing.T) {
	var expectedError errBadStatusCode
	_httpClient = newTestClient(func(req *http.Request) *http.Response {
		return &http.Response{
			StatusCode: 500,
			Body:       io.NopCloser(bytes.NewBufferString(`NOT OK`)),
			Header:     make(http.Header),
		}
	})

	ids, err := getAllHostIDs("" /*We don't need a token as we intercept the call*/)

	assert.Error(t, err)
	assert.ErrorAs(t, err, &expectedError)
	assert.Empty(t, ids)
}

func TestGetAllHostIDsOK(t *testing.T) {
	_httpClient = newTestClient(func(req *http.Request) *http.Response {
		r := zabbixResponse{
			JSONRPC: "2.0",
			ID:      1,
			Result: []map[string]interface{}{
				map[string]interface{}{
					"name":   "test",
					"hostid": "123",
				},
			},
		}
		b, _ := json.Marshal(r)
		return &http.Response{
			StatusCode: 200,
			Body:       io.NopCloser(bytes.NewBuffer(b)),
			Header:     make(http.Header),
		}
	})

	ids, err := getAllHostIDs("" /*We don't need a token as we intercept the call*/)

	assert.NoError(t, err)
	assert.NotEmpty(t, ids)
	assert.Equal(t, 1, len(ids))

	val, ok := ids["test"]
	assert.True(t, ok)
	assert.Equal(t, 123, val)
}

func TestGetAllHostIDsBadMapResponse(t *testing.T) {
	_httpClient = newTestClient(func(req *http.Request) *http.Response {
		r := zabbixResponse{
			JSONRPC: "2.0",
			ID:      1,
			Result:  1,
		}
		b, _ := json.Marshal(r)
		return &http.Response{
			StatusCode: 200,
			Body:       io.NopCloser(bytes.NewBuffer(b)),
			Header:     make(http.Header),
		}
	})

	ids, err := getAllHostIDs("" /*We don't need a token as we intercept the call*/)

	if err == nil {
		t.Errorf("Expected error but didn't get one")
	}
	if isBadStatusCodeError(err) == true {
		t.Errorf("Didn't expected an error of the type errBadStatusCode")
	}
	if isZabbixError(err) == false {
		t.Errorf("Expected zabbix error but got %T", err)
	} else {
		if err.(zabbixError).Code != 12345 {
			t.Errorf("Didn't get expected error code got: \nCode: %d\nError: %s", err.(zabbixError).Code, err)
		}
	}

	if len(ids) != 0 {
		t.Errorf("Expected empty map but got: %+v", ids)
	}
}

func TestGetAllHostIDsZabbixError(t *testing.T) {
	var expectedError zabbixError
	_httpClient = newTestClient(func(req *http.Request) *http.Response {
		r := zabbixResponse{
			JSONRPC: "2.0",
			ID:      1,
			Error: zabbixError{
				Code:    2,
				Message: "Test error",
				Data:    "Test error 2",
			},
		}
		b, _ := json.Marshal(r)
		return &http.Response{
			StatusCode: 200,
			Body:       io.NopCloser(bytes.NewBuffer(b)),
			Header:     make(http.Header),
		}
	})

	ids, err := getAllHostIDs("" /*We don't need a token as we intercept the call*/)

	assert.Error(t, err)
	assert.ErrorAs(t, err, &expectedError)
	assert.Empty(t, ids)
}
