package main

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetAuthTokenHTTPError(t *testing.T) {
	var expectedError errBadStatusCode
	_httpClient = newTestClient(func(req *http.Request) *http.Response {
		return &http.Response{
			StatusCode: 500,
			Body:       io.NopCloser(bytes.NewBufferString(`NOT OK`)),
			Header:     make(http.Header),
		}
	})

	s, err := getAuthToken()
	assert.Error(t, err)
	assert.ErrorAs(t, err, &expectedError)
	assert.Empty(t, s)
}

func TestGetAuthTokenOK(t *testing.T) {
	_httpClient = newTestClient(func(req *http.Request) *http.Response {
		r := zabbixResponse{
			JSONRPC: "2.0",
			ID:      1,
			Result:  "token",
		}
		b, _ := json.Marshal(r)
		return &http.Response{
			StatusCode: 200,
			Body:       io.NopCloser(bytes.NewBuffer(b)),
			Header:     make(http.Header),
		}
	})

	s, err := getAuthToken()

	assert.NoError(t, err)
	assert.Equal(t, "token", s)
}

func TestGetAuthTokenBadTokenResponse(t *testing.T) {
	_httpClient = newTestClient(func(req *http.Request) *http.Response {
		r := zabbixResponse{
			JSONRPC: "2.0",
			ID:      1,
			Result:  1,
		}
		b, _ := json.Marshal(r)
		return &http.Response{
			StatusCode: 200,
			Body:       io.NopCloser(bytes.NewBuffer(b)),
			Header:     make(http.Header),
		}
	})

	s, err := getAuthToken()

	assert.Error(t, err)
	assert.Empty(t, s)
}

func TestGetAuthTokenZabbixError(t *testing.T) {
	var expectedError zabbixError
	_httpClient = newTestClient(func(req *http.Request) *http.Response {
		r := zabbixResponse{
			JSONRPC: "2.0",
			ID:      1,
			Error: zabbixError{
				Code:    2,
				Message: "Test error",
				Data:    "Test error 2",
			},
		}
		b, _ := json.Marshal(r)
		return &http.Response{
			StatusCode: 200,
			Body:       io.NopCloser(bytes.NewBuffer(b)),
			Header:     make(http.Header),
		}
	})

	s, err := getAuthToken()

	assert.Error(t, err)
	assert.ErrorAs(t, err, &expectedError)
	assert.Empty(t, s)
}
