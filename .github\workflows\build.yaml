---
name: Build
on:
  schedule:
    - cron:  '23 4 * * 2' # Every Tuesday night
  workflow_dispatch:
  push:
    tags: [ 'v*.*.*' ]
permissions:
  contents: read
  packages: write
concurrency:
  group: build-${{ github.head_ref || github.ref_name }}
  cancel-in-progress: true

jobs:
  autotests:
    uses: ./.github/workflows/autotests.yaml
    secrets: inherit
  docker:
    needs: autotests
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 100

      - name: Ensure tags are fetched
        run: git fetch --tags

      - name: Get latest tag
        id: git-latest-tag
        run: |
          o=$(git describe --tags --abbrev=0)
          echo "latest=${o}" >> $GITHUB_OUTPUT

      - name: Checkout latest tag
        if: "!startsWith(github.ref, 'refs/tags/')"
        run: "git checkout ${{ steps.git-latest-tag.outputs.latest }}"

      - name: Build and push Docker image
        uses: scalepoint/actions/docker/build-push@v1
        with:
          image-name: 'ito-zabbix-maintenance-api'
          image-version-convention: semver
          image-semver-main-version: '1.6.5'
          context: .
          file: ./Dockerfile
          provenance: false
          git-tag-push-enabled: false
          git-tag-prefix: image
          github-token: ${{ secrets.GITHUB_TOKEN }}
