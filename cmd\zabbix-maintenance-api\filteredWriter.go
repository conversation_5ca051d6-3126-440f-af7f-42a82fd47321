package main

import (
	"io"

	"github.com/rs/zerolog"
)

type filteredWriter struct {
	lw         io.Writer
	level      zerolog.Level
	exactMatch bool
}

func (w *filteredWriter) WriteLevel(l zerolog.Level, p []byte) (n int, err error) {
	if l >= w.level && w.exactMatch == false {
		return w.lw.Write(p)
	}
	if l == w.level && w.exactMatch == true {
		return w.lw.Write(p)
	}
	return len(p), nil
}

func (w *filteredWriter) Write(p []byte) (n int, err error) {
	return w.lw.Write(p)
}

func FilteredWriter(w io.Writer, l zerolog.Level, m bool) io.Writer {
	return &filteredWriter{lw: w, level: l, exactMatch: m}
}
