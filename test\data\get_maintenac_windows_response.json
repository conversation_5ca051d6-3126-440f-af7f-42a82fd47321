{"jsonrpc": "2.0", "result": [{"maintenanceid": "62", "name": "prod-ecc-ecc01 -restart tomcat", "maintenance_type": "0", "description": "Tomcat", "active_since": "1598295000", "active_till": "1755976800", "tags_evaltype": "0", "groups": [], "hosts": [{"hostid": "11323", "proxy_hostid": "0", "host": "prod-ecc-ecc01.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "prod-ecc-ecc01.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}]}, {"maintenanceid": "30", "name": "prod-ecc-ecc02 -restart tomcat", "maintenance_type": "0", "description": "Tomcat", "active_since": "1590690540", "active_till": "1906311000", "tags_evaltype": "0", "groups": [], "hosts": [{"hostid": "11322", "proxy_hostid": "0", "host": "prod-ecc-ecc02.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "prod-ecc-ecc02.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}]}, {"maintenanceid": "67", "name": "ecc reindexation", "maintenance_type": "0", "description": "", "active_since": "1598824800", "active_till": "1661983200", "tags_evaltype": "0", "groups": [], "hosts": [{"hostid": "11322", "proxy_hostid": "0", "host": "prod-ecc-ecc02.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "prod-ecc-ecc02.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}, {"hostid": "11323", "proxy_hostid": "0", "host": "prod-ecc-ecc01.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "prod-ecc-ecc01.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}]}, {"maintenanceid": "91", "name": "reindex SQL PROD ECC", "maintenance_type": "0", "description": "SQL", "active_since": "1611010800", "active_till": "1738278000", "tags_evaltype": "0", "groups": [], "hosts": [{"hostid": "11322", "proxy_hostid": "0", "host": "prod-ecc-ecc02.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "prod-ecc-ecc02.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}, {"hostid": "11323", "proxy_hostid": "0", "host": "prod-ecc-ecc01.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "prod-ecc-ecc01.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}]}, {"maintenanceid": "92", "name": "Foreman - 50% of hosts have notify status for more than 5 minutes", "maintenance_type": "0", "description": "", "active_since": "1611010800", "active_till": "1738278000", "tags_evaltype": "0", "groups": [], "hosts": [{"hostid": "10160", "proxy_hostid": "0", "host": "foreman.spcph.local", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "0", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "foreman.spcph.local", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}]}, {"maintenanceid": "7", "name": "Conference PC", "maintenance_type": "0", "description": "Windows update", "active_since": "1548105600", "active_till": "1864101600", "tags_evaltype": "0", "groups": [{"groupid": "18", "name": "Conference PC", "internal": "0", "flags": "0"}], "hosts": []}, {"maintenanceid": "94", "name": "hosts- jan", "maintenance_type": "0", "description": "", "active_since": "1622239200", "active_till": "1622325600", "tags_evaltype": "0", "groups": [], "hosts": [{"hostid": "11352", "proxy_hostid": "0", "host": "nghw-6032.scalepoint.tech", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "nghw-6032.scalepoint.tech", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}, {"hostid": "11354", "proxy_hostid": "0", "host": "nghw-6033.scalepoint.tech", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "nghw-6033.scalepoint.tech", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}, {"hostid": "11358", "proxy_hostid": "0", "host": "nghw-6035.scalepoint.tech", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "nghw-6035.scalepoint.tech", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}, {"hostid": "11363", "proxy_hostid": "0", "host": "nghw-clu1.scalepoint.tech", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "nghw-clu1.scalepoint.tech", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}]}, {"maintenanceid": "38", "name": "PROD-ECA- DEPLOY", "maintenance_type": "0", "description": "Deploy", "active_since": "1591740000", "active_till": "1688076000", "tags_evaltype": "0", "groups": [], "hosts": [{"hostid": "10290", "proxy_hostid": "0", "host": "audit-prod01.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "0", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "audit-prod01.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}, {"hostid": "10982", "proxy_hostid": "0", "host": "audit-prod02.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "audit-prod02.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}]}, {"maintenanceid": "40", "name": "PROD-ECC-DEPLOY", "maintenance_type": "0", "description": "Deploy", "active_since": "1591740000", "active_till": "1687298400", "tags_evaltype": "0", "groups": [], "hosts": [{"hostid": "11322", "proxy_hostid": "0", "host": "prod-ecc-ecc02.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "prod-ecc-ecc02.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}, {"hostid": "11323", "proxy_hostid": "0", "host": "prod-ecc-ecc01.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "prod-ecc-ecc01.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}]}, {"maintenanceid": "35", "name": "TEST - ECB - DEPLOYS", "maintenance_type": "0", "description": "Deploy", "active_since": "1591740000", "active_till": "1687903200", "tags_evaltype": "0", "groups": [], "hosts": [{"hostid": "10710", "proxy_hostid": "0", "host": "sandbox.scalepoint.com", "status": "1", "disable_until": "0", "error": "", "available": "0", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "sandbox.scalepoint.com", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "-1"}, {"hostid": "11013", "proxy_hostid": "0", "host": "test-ecb-wfe01.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "test-ecb-wfe01.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}, {"hostid": "11015", "proxy_hostid": "0", "host": "test-ecb-wfe04.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "test-ecb-wfe04.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}, {"hostid": "11016", "proxy_hostid": "0", "host": "test-ecb-wfe03.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "test-ecb-wfe03.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}, {"hostid": "11017", "proxy_hostid": "0", "host": "test-ecb-wfe02.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "test-ecb-wfe02.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}, {"hostid": "11234", "proxy_hostid": "0", "host": "test-ecb-wfe05.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "test-ecb-wfe05.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}, {"hostid": "11347", "proxy_hostid": "0", "host": "test-ecb-wfe06.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "test-ecb-wfe06.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}, {"hostid": "11348", "proxy_hostid": "0", "host": "test-ecb-wfe07.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "test-ecb-wfe07.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}, {"hostid": "11380", "proxy_hostid": "0", "host": "test-ecb-wfe08.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "test-ecb-wfe08.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}]}, {"maintenanceid": "36", "name": "TEST - ECA - Audit - DEPLOYS", "maintenance_type": "0", "description": "Deploy", "active_since": "1591740000", "active_till": "1687384800", "tags_evaltype": "0", "groups": [], "hosts": [{"hostid": "10204", "proxy_hostid": "0", "host": "audit-test01.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "0", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "audit-test01.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}, {"hostid": "10999", "proxy_hostid": "0", "host": "audit-test02.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "audit-test02.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}]}, {"maintenanceid": "85", "name": "PROD-Entry.scalepoint- DEPLOY", "maintenance_type": "0", "description": "Deploy", "active_since": "1591740000", "active_till": "1687989600", "tags_evaltype": "0", "groups": [], "hosts": [{"hostid": "10438", "proxy_hostid": "0", "host": "entry.scalepoint.com", "status": "0", "disable_until": "0", "error": "", "available": "0", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "entry.scalepoint.com", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}]}, {"maintenanceid": "39", "name": "PROD-ECB-DEPLOY", "maintenance_type": "0", "description": "Deploy", "active_since": "1591740000", "active_till": "1687298400", "tags_evaltype": "0", "groups": [], "hosts": [{"hostid": "10438", "proxy_hostid": "0", "host": "entry.scalepoint.com", "status": "0", "disable_until": "0", "error": "", "available": "0", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "entry.scalepoint.com", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}, {"hostid": "11021", "proxy_hostid": "0", "host": "prod-ecb-wfe01.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "prod-ecb-wfe01.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}, {"hostid": "11345", "proxy_hostid": "0", "host": "prod-ecb-wfe07.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "prod-ecb-wfe07.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}, {"hostid": "11346", "proxy_hostid": "0", "host": "prod-ecb-wfe06.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "prod-ecb-wfe06.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}, {"hostid": "11381", "proxy_hostid": "0", "host": "prod-ecb-wfe08.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "prod-ecb-wfe08.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}, {"hostid": "12938", "proxy_hostid": "0", "host": "prod-ecb-wfe04.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "prod-ecb-wfe04.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}, {"hostid": "12940", "proxy_hostid": "0", "host": "prod-ecb-wfe03.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "prod-ecb-wfe03.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}, {"hostid": "12941", "proxy_hostid": "0", "host": "prod-ecb-wfe02.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "prod-ecb-wfe02.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}, {"hostid": "12942", "proxy_hostid": "0", "host": "prod-ecb-wfe05.scalepoint.lan", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "prod-ecb-wfe05.scalepoint.lan", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}]}, {"maintenanceid": "98", "name": "wsus", "maintenance_type": "0", "description": "", "active_since": "1626904800", "active_till": "1627077600", "tags_evaltype": "0", "groups": [], "hosts": [{"hostid": "12790", "proxy_hostid": "0", "host": "int-ito-wsus01.spcph.local", "status": "0", "disable_until": "0", "error": "", "available": "1", "errors_from": "0", "lastaccess": "0", "ipmi_authtype": "-1", "ipmi_privilege": "2", "ipmi_username": "", "ipmi_password": "", "ipmi_disable_until": "0", "ipmi_available": "0", "snmp_disable_until": "0", "snmp_available": "0", "maintenanceid": "0", "maintenance_status": "0", "maintenance_type": "0", "maintenance_from": "0", "ipmi_errors_from": "0", "snmp_errors_from": "0", "ipmi_error": "", "snmp_error": "", "jmx_disable_until": "0", "jmx_available": "0", "jmx_errors_from": "0", "jmx_error": "", "name": "int-ito-wsus01.spcph.local", "flags": "0", "templateid": "0", "description": "", "tls_connect": "1", "tls_accept": "1", "tls_issuer": "", "tls_subject": "", "tls_psk_identity": "", "tls_psk": "", "proxy_address": "", "auto_compress": "1", "discover": "0", "custom_interfaces": "0", "inventory_mode": "1"}]}], "id": 3}