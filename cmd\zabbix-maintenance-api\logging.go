package main

import (
	"io"
	"os"
	"path"
	"strconv"
	"strings"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/pkgerrors"
	"gopkg.in/natefinch/lumberjack.v2"
)

var log zerolog.Logger

type LogConfig struct {
	CLILogLevel   string
	FileLogLevel  string
	DebugLog      bool
	TraceLog      bool
	DebugOnly     bool
	TraceOnly     bool
	Filename      string
	LogMaxSize    int
	LogMaxBackups int
	LogMaxAge     int
}

func logger(cfg LogConfig) {
	var writers []io.Writer

	// filename and line number instead of full path and line number
	zerolog.CallerMarshalFunc = func(pc uintptr, file string, line int) string {
		short := file
		for i := len(file) - 1; i > 0; i-- {
			if file[i] == '/' {
				short = file[i+1:]
				break
			}
		}
		file = short
		return file + ":" + strconv.Itoa(line)
	}

	zerolog.ErrorStackMarshaler = pkgerrors.MarshalStack
	// tmp instance used for handling errors before the logger is configured
	log = zerolog.New(os.Stderr).With().Timestamp().Caller().Logger()

	cliLevel := zerolog.InfoLevel
	if l, e := zerolog.ParseLevel(cfg.CLILogLevel); e != nil {
		log.Error().Stack().Str("Level", cfg.CLILogLevel).Err(e).Msg("Logging: Failed parse cli log level")
	} else {
		cliLevel = l
	}

	// if no debug or trace log have been set use the cli level
	// else use the lowest level needed
	if cfg.DebugLog == false && cfg.TraceLog == false {
		zerolog.SetGlobalLevel(cliLevel)
	} else if cfg.TraceLog == true {
		zerolog.SetGlobalLevel(zerolog.TraceLevel)
	} else if cfg.DebugLog == true {
		zerolog.SetGlobalLevel(zerolog.DebugLevel)
	}

	writers = append(writers, FilteredWriter(zerolog.ConsoleWriter{Out: os.Stdout, NoColor: true}, cliLevel, false))

	if cfg.DebugLog == true {
		p, err := os.Getwd()
		if err != nil {
			log.Error().Stack().Err(err).Msg("Logging: Failed to get current working directory")
		} else {
			dp := path.Join(p, "debug.log")
			lj := lumberjack.Logger{
				Filename:   dp,
				MaxSize:    cfg.LogMaxSize, // megabytes
				MaxBackups: cfg.LogMaxBackups,
				MaxAge:     cfg.LogMaxAge, //days
			}
			debuglog := FilteredWriter(&lj, zerolog.DebugLevel, cfg.DebugOnly)
			writers = append(writers, debuglog)
		}
	}
	if cfg.TraceLog == true {
		p, err := os.Getwd()
		if err != nil {
			log.Error().Stack().Err(err).Msg("Logging: Failed to get current working directory")
		} else {
			dp := path.Join(p, "trace.log")
			lj := lumberjack.Logger{
				Filename:   dp,
				MaxSize:    cfg.LogMaxSize, // megabytes
				MaxBackups: cfg.LogMaxBackups,
				MaxAge:     cfg.LogMaxAge, //days
			}
			tracelog := FilteredWriter(&lj, zerolog.TraceLevel, cfg.TraceOnly)
			writers = append(writers, tracelog)
		}
	}
	if cfg.Filename != "" {
		p, err := os.Getwd()
		if err != nil {
			log.Error().Stack().Err(err).Msg("Logging: Failed to get current working directory")
		} else {
			fileLevel := zerolog.InfoLevel
			if l, e := zerolog.ParseLevel(cfg.FileLogLevel); e != nil {
				log.Error().Stack().Str("Level", cfg.FileLogLevel).Err(e).Msg("Logging: Failed parse file log level")
			} else {
				fileLevel = l
			}

			var dp string
			// if the first char is not a `/` then add the current folder
			// to the file path
			if strings.HasPrefix(cfg.Filename, "/") == false {
				dp = path.Join(p, cfg.Filename)
			} else {
				dp = cfg.Filename
			}
			lj := lumberjack.Logger{
				Filename:   dp,
				MaxSize:    cfg.LogMaxSize, // megabytes
				MaxBackups: cfg.LogMaxBackups,
				MaxAge:     cfg.LogMaxAge, //days
			}
			filelog := FilteredWriter(&lj, fileLevel, false)
			writers = append(writers, filelog)
		}
	}

	multi := zerolog.MultiLevelWriter(writers...)

	log = zerolog.New(multi).With().Timestamp().Caller().Logger()

	log.Debug().
		Str("CLILogLevel", cfg.CLILogLevel).
		Str("GlobalLog Level", log.GetLevel().String()).
		Bool("DebugLog", cfg.DebugLog).
		Bool("DebugOnly", cfg.DebugOnly).
		Bool("TraceLog", cfg.TraceLog).
		Bool("TraceOnly", cfg.TraceOnly).
		Msg("Logging: Logging configured")
}
