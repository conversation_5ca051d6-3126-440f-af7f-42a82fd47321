package main

import (
	"os"
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIsValidEmptyConfig(t *testing.T) {
	cfg := config{}

	assert.False(t, cfg.isValid())
}

func TestIsValidFullConfig(t *testing.T) {
	cfg := config{
		ZabbixHost:                  "test",
		ZabbixUser:                  "test",
		ZabbixPassword:              "test",
		ZabbixSchema:                "test",
		ZabbixPort:                  1,
		ZabbixDowntime:              1,
		SkipCertificateVerification: true,
		StatusCakeAPIToken:          "test",
	}

	assert.True(t, cfg.isValid())
}

func TestLoadConfigMissingFile(t *testing.T) {
	resetENV()

	var cfg config
	err := cfg.load("missing.config")

	assert.Error(t, err)
	assert.False(t, cfg.isValid())

}

func TestLoadConfigDefaultValues(t *testing.T) {
	resetENV()

	var cfg config
	err := cfg.load("")

	assert.NoError(t, err)

	assert.Equal(t, "prod-zabbix.scalepoint.tech", cfg.ZabbixHost)
	assert.Equal(t, "https", cfg.ZabbixSchema)
	assert.Equal(t, 443, cfg.ZabbixPort)
	assert.Equal(t, 10800, cfg.ZabbixDowntime)
	assert.False(t, cfg.SkipCertificateVerification)

	// These don't have defaults and should be empty
	assert.Empty(t, cfg.StatusCakeAPIToken)
	assert.Empty(t, cfg.ZabbixUser)
	assert.Empty(t, cfg.ZabbixPassword)

}

func resetENV() {
	var cfg config
	val := reflect.ValueOf(cfg)
	for i := 0; i < val.NumField(); i++ {
		os.Unsetenv(val.Type().Field(i).Name)
	}
}
