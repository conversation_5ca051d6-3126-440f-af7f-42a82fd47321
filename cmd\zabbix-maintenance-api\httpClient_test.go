package main

import (
	"bytes"
	"io"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestInitHTTPClient(t *testing.T) {
	_httpClient = nil

	initHTTPClient()

	assert.NotEmpty(t, _httpClient)
}

func TestHTTPRequestDryRunStopsCreateRequest(t *testing.T) {
	_dryRun = true
	var expectedError errDryRun
	request := zabbixRequest{
		Method: "create",
	}

	stream, err := httpRequest("", request, "GET")

	assert.<PERSON>rror(t, err)
	assert.ErrorAs(t, err, &expectedError)
	assert.Nil(t, stream)
	assert.Equal(t, request.Method, err.(errDryRun).ZabbixMethod)
	_dryRun = false
}

func TestHTTPRequestDryRunStopsDeleteRequest(t *testing.T) {
	_dryRun = true
	var expectedError errDryRun
	request := zabbixRequest{
		Method: "delete",
	}

	stream, err := httpRequest("", request, "GET")

	assert.Error(t, err)
	assert.ErrorAs(t, err, &expectedError)
	assert.Nil(t, stream)
	assert.Equal(t, request.Method, err.(errDryRun).ZabbixMethod)
	_dryRun = false
}

func TestHTTPRequestDryRunDontStopUserRequest(t *testing.T) {
	_dryRun = true
	_httpClient = newTestClient(func(req *http.Request) *http.Response {
		return &http.Response{
			StatusCode: 200,
			Body:       io.NopCloser(bytes.NewBufferString(`OK`)),
			Header:     make(http.Header),
		}
	})
	request := zabbixRequest{
		Method: "user.checkAuthentication",
	}

	stream, err := httpRequest("", request, "GET")

	assert.NoError(t, err)
	assert.NotEmpty(t, stream)
	_dryRun = false
}

func TestHTTPRequest200(t *testing.T) {
	_httpClient = newTestClient(func(req *http.Request) *http.Response {
		return &http.Response{
			StatusCode: 200,
			Body:       io.NopCloser(bytes.NewBufferString(`OK`)),
			Header:     make(http.Header),
		}
	})

	stream, err := httpRequest("", zabbixRequest{}, "GET")

	assert.NoError(t, err)
	assert.NotEmpty(t, stream)

}

func TestHTTPRequest500(t *testing.T) {
	_httpClient = newTestClient(func(req *http.Request) *http.Response {
		return &http.Response{
			StatusCode: 500,
			Body:       io.NopCloser(bytes.NewBufferString(`Not OK`)),
			Header:     make(http.Header),
		}
	})
	var expectedError errBadStatusCode

	stream, err := httpRequest("", zabbixRequest{}, "GET")

	assert.Error(t, err)
	assert.ErrorAs(t, err, &expectedError)
	assert.NotEmpty(t, stream)
}
