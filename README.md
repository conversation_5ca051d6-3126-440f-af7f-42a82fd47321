# Zabbix maintenace api

This api is made to simplify putting servers in maintenance mode without granting rigths to zabbix.\
Its works by calling a specific URL with an identifier and that identifier is then used to identify what servers will be added to the maintenace window. The maintenace window created will be 3 hours long unless otherwise specificed in the configuration file. Maintenace window can be removed early by calling a URL with the same identifier used for creating the maintence winodw, this will cause the window to be removed.


## URLS
 * https://prod-zabbix-api.scalepoint.tech/<identifier\>/add - Create maintence window
 * https://prod-zabbix-api.scalepoint.tech/<identifier\>/remove - Remove maintenace window
 * https://prod-zabbix-api.scalepoint.tech/<FQDN\>/add - Create maintence window for a specific host
 * https://prod-zabbix-api.scalepoint.tech/<FQDN\>/remove - Remove maintenace window for a specific host
 * https://prod-zabbix-api.scalepoint.tech/identifiers - list identifers and servers attached to them
 * https://prod-zabbix-api.scalepoint.tech/list/cache - list all cache entries (hostname to zabbix id)
