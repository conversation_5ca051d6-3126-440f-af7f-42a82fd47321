package main

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestRequestMaintenanceHTTPError(t *testing.T) {
	var expectedError errBadStatusCode
	_httpClient = newTestClient(func(req *http.Request) *http.Response {
		return &http.Response{
			StatusCode: 500,
			Body:       io.NopCloser(bytes.NewBufferString(`NOT OK`)),
			Header:     make(http.Header),
		}
	})

	output, err := requestMaintenance("", []int{}, "" /*We don't need a token, name or ids as we intercept the call*/)

	assert.Error(t, err)
	assert.ErrorAs(t, err, &expectedError)
	assert.Empty(t, output)
}

func TestRequestMaintenanceOK(t *testing.T) {
	_httpClient = newTestClient(func(req *http.Request) *http.Response {
		b, _ := os.ReadFile("../../test/data/create_maintenac_windows_response.json")
		return &http.Response{
			StatusCode: 200,
			Body:       io.NopCloser(bytes.NewBuffer(b)),
			Header:     make(http.Header),
		}
	})

	output, err := requestMaintenance("", []int{}, "" /*We don't need a token, name or ids as we intercept the call*/)

	assert.NoError(t, err)
	assert.NotEmpty(t, output)
}
func TestRequestMaintenanceZabbixError(t *testing.T) {
	var expectedError zabbixError
	_httpClient = newTestClient(func(req *http.Request) *http.Response {
		r := zabbixResponse{
			JSONRPC: "2.0",
			ID:      1,
			Error: zabbixError{
				Code:    2,
				Message: "Test error",
				Data:    "Test error 2",
			},
		}
		b, _ := json.Marshal(r)
		return &http.Response{
			StatusCode: 200,
			Body:       io.NopCloser(bytes.NewBuffer(b)),
			Header:     make(http.Header),
		}
	})

	output, err := requestMaintenance("", []int{}, "" /*We don't need a token, name or ids as we intercept the call*/)

	assert.Error(t, err)
	assert.ErrorAs(t, err, &expectedError)
	assert.Empty(t, output)
}
