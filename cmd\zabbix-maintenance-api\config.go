package main

import (
	"os"
	"reflect"
	"strconv"
	"strings"

	"github.com/joho/godotenv"
)

var (
	Version string
	GitHash string
)

type config struct {
	ZabbixHost                  string `default:"prod-zabbix.scalepoint.tech"`
	ZabbixUser                  string
	ZabbixPassword              string
	ZabbixSchema                string `default:"https"`
	ZabbixPort                  int    `default:"443"`
	ZabbixDowntime              int    `default:"10800"` // 3 * 3600 aka 3 hours of downtime
	SkipCertificateVerification bool   `default:"false"`
	StatusCakeAPIToken          string
}

func (c *config) load(filename string) error {
	log.Trace().Msg("Entered function load on config")

	if filename != "" {
		log.Trace().Str("Filename", filename).Msg("Config: Loading config file")
		err := godotenv.Overload(filename)
		if err != nil {
			log.Debug().Msg("Config: Failed to load config file")
			return err
		}
	}

	val := reflect.ValueOf(c).Elem()

	log.Trace().Int("Fields", val.NumField()).Msg("Config: Get reflect of config")

	for i := 0; i < val.NumField(); i++ {
		field := val.Type().Field(i)
		value := val.Field(i)

		slog := log.With().Str("Field", field.Name).Logger()

		if value.CanSet() == false {
			slog.Trace().Msg("Config: Unable to set value on field via reflection")
			continue
		}

		slog.Trace().Msg("Config: Checking for envorinment variable for field")
		if v := os.Getenv(field.Name); v != "" {
			slog.Trace().Msg("Config: Setting field value to value of environment variable")
			e := setFieldValue(value, v, field.Name)
			if e != nil {
				return e
			}
		} else {
			slog.Trace().Msg("Config: No environment variable found, checking for default tag")

			if v, ok := field.Tag.Lookup("default"); ok == true && v != "" {
				slog.Trace().Msg("Config: Found default value")
				e := setFieldValue(value, v, field.Name)
				if e != nil {
					return e
				}
			}
		}
	}

	return nil
}

func setFieldValue(field reflect.Value, value, name string) error {
	log.Trace().Msg("Entered function setFieldValue")
	slog := log.With().Str("Value", value).Str("Field", name).Logger()

	switch field.Kind() {
	case reflect.String:
		log.Trace().Msg("Set Field Value: Setting value on string field")
		field.SetString(value)
	case reflect.Bool:
		log.Trace().Msg("Set Field Value: Setting value on boolean field")
		field.SetBool(strings.EqualFold(value, "true"))
	case reflect.Int:
		log.Trace().Msg("Set Field Value: Setting value on int field")
		if i, err := strconv.Atoi(value); err == nil {
			x := int64(i)
			if field.OverflowInt(x) == true {
				slog.Warn().Msg("Set Field Value: Setting value would cause an overflow")
				return errSetValue{FieldName: name, FieldType: "Int"}
			} else {
				field.SetInt(x)
			}
		} else {
			slog.Warn().Msg("Set Field Value: Failed to convert value to int")
			return errSetValue{FieldName: name, FieldType: "Int", Err: err}
		}
	default:
		slog.Debug().Msgf("Set Field Value: Unexpected field type %v", field.Kind())
		return errSetValue{FieldName: name, FieldType: "Int", UnexpectedType: true}
	}

	return nil
}

func (c config) isValid() bool {
	log.Trace().Msg("Enter function isValid on config")
	valid := true

	val := reflect.ValueOf(&c).Elem()

	log.Trace().Int("Fields", val.NumField()).Msg("Validate config: Get reflect of config")

	for i := 0; i < val.NumField(); i++ {
		field := val.Type().Field(i)
		value := val.Field(i)

		slog := log.With().Str("Field", field.Name).Logger()
		slog.Trace().Msg("Validate config: Checking for default tag")
		if v, ok := field.Tag.Lookup("default"); ok == false || v == "" {
			slog.Trace().Msg("Validate config: No default tag found, checking if field has a value")
			switch value.Kind() {
			case reflect.String:
				slog.Trace().Msg("Validate config: Checking value of string field")
				if value.String() == "" {
					valid = false
					log.Warn().Msgf("Validate config: Missing value for %s", field.Name)
				}
				slog.Trace().Msgf("Validate config: Value of string field %s is %s", field.Name, value.String())
			default:
				valid = false
				slog.Debug().Msgf("Validate config: Unexpected field type %v", value.Kind())
				slog.Trace().Msgf("Validate config: Value of field %s is %v", field.Name, value)
			}
		}
	}

	return valid
}
