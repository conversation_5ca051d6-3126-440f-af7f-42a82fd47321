.PHONY: build format test dockerbuild
default: format test build

VERSION=$$(git describe --tags --abbrev=0)
GITHASH=$$(git rev-parse HEAD)


build:
	go build -trimpath -ldflags="-s -w -X 'main.Version=$(VERSION)' -X 'main.GitHash=$(GITHASH)'" -o ./bin/ ./cmd/zabbix-maintenance-api
format:
	go mod tidy
	gofmt -w ./
test:
	go vet ./...
	go test ./...
	staticcheck ./...
	@! grep -q ':"' mappings.yaml
dockerbuild:
	docker build -t zabbix-maintenance-api .
	docker tag zabbix-maintenance-api:latest itoacr.azurecr.io/ito/zabbix-maintenance-api:$(VERSION)

restart: build
	test/scripts/restartserver.sh
