package main

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"io"
	"net/http"
	"strings"
	"sync"
	"time"
)

var _httpClient *http.Client

// used by initHTTPClient
var _httpClientLock sync.Mutex

func initHTTPClient() {
	log.Trace().Msg("Entered function initHTTPClient")

	// If the http client has already been initialized we don't need
	// to initialize it once more
	if _httpClient != nil {
		log.Trace().Msg("HTTP Client: Client already initialized")
		return
	}

	// Aquire the http client lock to ensure we only initialize one
	// instance of the http client
	log.Trace().Msg("HTTP Client: Requesting HTTP Client Lock")
	_httpClientLock.Lock()

	// ensure that the lock is released after we have used it
	log.Trace().Msg("HTTP Client: Defering HTTP Client Unlock")
	defer _httpClientLock.Unlock()
	defer log.Trace().Msg("HTTP Client: HTTP Client Lock released")

	log.Trace().Msg("HTTP Client: HTTP Client Lock aquired")

	// The http client might have been initialized while
	// we waited for the lock, so we need to check aging
	if _httpClient != nil {
		log.Trace().Msg("HTTP Client: Client already initialized")
		return
	}

	log.Debug().Msg("HTTP Client: Initializing HTTP Client")

	tr := &http.Transport{
		MaxIdleConns:    10,
		IdleConnTimeout: 30 * time.Second,
		TLSClientConfig: &tls.Config{InsecureSkipVerify: _cfg.SkipCertificateVerification},
	}

	_httpClient = &http.Client{Transport: tr}
}

func httpRequest(url string, request zabbixRequest, method string) (io.ReadCloser, error) {
	log.Trace().Msg("Entered function httpRequest")

	slog := log.With().Int("RequestID", request.ID).Str("URL", url).Str("Method", method).Logger()

	slog.Trace().Msg("HTTP Request: Ensure we have a HTTP client")
	initHTTPClient()

	// zabbix 7.2 have changed the way authentication is handled
	// and this is the simple way to handle it
	token := request.Auth
	request.Auth = ""

	slog.Trace().Msg("HTTP Request: Convert zabbix request to JSON")
	payload, err := json.Marshal(request)
	if err != nil {
		slog.Trace().Err(err).Msg("HTTP Request: Failed to convert zabbix request to JSON")
		return nil, err
	}
	slog.Trace().RawJSON("Payload", payload).Msg("HTTP Request: Zabbix request converted to JSON")

	slog.Trace().Int("Request Size", len(payload)).Msg("HTTP Request: Creating HTTP request")
	req, err := http.NewRequest(method, url, bytes.NewBuffer(payload))
	if err != nil {
		slog.Trace().Err(err).Msg("HTTP Request: Failed to create HTTP request")
		return nil, err
	}

	// Set a user-agent to make is easier to identify the
	// application in the logs
	slog.Trace().Msg("HTTP Request: Setting HTTP headers")
	req.Header.Set("User-Agent", "zabbix-maintenance")
	req.Header.Set("Content-Type", "application/json")
	if token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
	}

	if _dryRun {
		m := strings.ToLower(request.Method)
		if strings.Contains(m, "create") || strings.Contains(m, "delete") {
			slog.Trace().Msg("HTTP Request: Dropping request as we are running in dryrun mode")
			return nil, errDryRun{ZabbixMethod: request.Method}
		}
	}
	slog.Trace().Msg("HTTP Request: Sending HTTP request to zabbix")
	res, err := _httpClient.Do(req)
	if err != nil {
		slog.Trace().Err(err).Msg("HTTP Request: Failed to sent HTTP request to zabbix")
		return nil, err
	}
	slog.Trace().Msg("HTTP Request: Request was sent without issues to zabbix")

	// Zabbix always return a 2xx http code even when the api call caused
	// errors so if we don't receive a 2xx http code something must have
	// gone wrong with our request or on the receiving end. The Body is
	// included as an error message could be included
	if res.StatusCode < 200 || res.StatusCode >= 300 {
		slog.Trace().Int("StatusCode", res.StatusCode).Str("Status", res.Status).Msg("HTTP Request: Zabbix reponse didn't have a status code between 200 and 299")
		return res.Body, errBadStatusCode{Code: res.StatusCode, Status: res.Status}
	}

	return res.Body, nil
}
