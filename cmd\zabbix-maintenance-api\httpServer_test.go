package main

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestReadUserIPRemoteAddr(t *testing.T) {
	input := http.Request{
		RemoteAddr: "*******"}
	expectedOutput := "*******"

	input.Header = make(map[string][]string)

	output := readUserIP(&input)

	assert.Equal(t, expectedOutput, output)
}

func TestReadUserIPRemoteAddrWithPort(t *testing.T) {
	input := http.Request{
		RemoteAddr: "*******:5555"}
	expectedOutput := "*******"

	input.Header = make(map[string][]string)

	output := readUserIP(&input)

	assert.Equal(t, expectedOutput, output)
}

func TestReadUserIPXRealIP(t *testing.T) {
	input := http.Request{}
	expectedOutput := ", *******"
	headerValues := map[string]string{
		"X-Real-IP": "*******",
	}

	input.Header = make(map[string][]string)
	for k, v := range headerValues {
		input.Header.Add(k, v)
	}

	output := readUserIP(&input)

	assert.Equal(t, expectedOutput, output)
}

func TestReadUserIPXForwaredForSingleIP(t *testing.T) {
	input := http.Request{}
	expectedOutput := ", *******"
	headerValues := map[string]string{
		"X-Forwarded-For": "*******",
	}

	input.Header = make(map[string][]string)
	for k, v := range headerValues {
		input.Header.Add(k, v)
	}

	output := readUserIP(&input)

	assert.Equal(t, expectedOutput, output)
}

func TestReadUserIPXForwaredForMultipleIPs(t *testing.T) {
	input := http.Request{}
	expectedOutput := ", *******, *******"
	headerValues := map[string]string{
		"X-Forwarded-For": "*******, *******",
	}

	input.Header = make(map[string][]string)
	for k, v := range headerValues {
		input.Header.Add(k, v)
	}

	output := readUserIP(&input)

	assert.Equal(t, expectedOutput, output)
}

func TestReadUserIPRemoteaddrAndXRealIP(t *testing.T) {
	input := http.Request{
		RemoteAddr: "*******"}
	expectedOutput := "*******, *******"
	headerValues := map[string]string{
		"X-Real-IP": "*******",
	}

	input.Header = make(map[string][]string)
	for k, v := range headerValues {
		input.Header.Add(k, v)
	}

	output := readUserIP(&input)

	assert.Equal(t, expectedOutput, output)
}
