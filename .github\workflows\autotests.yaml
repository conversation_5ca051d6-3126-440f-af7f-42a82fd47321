---
name: Autotests
on:
  workflow_call
jobs:
  autotest-run:
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version-file: 'go.mod'
          cache-dependency-path: 'go.sum'

      - name: Install dependencies
        run: go mod download

      - name: Install staticcheck
        run: go install honnef.co/go/tools/cmd/staticcheck@2024.1.1

      - name: go vet
        run: go vet -v ./...

      - name: go test
        run: go test -v ./...

      - name: staticcheck
        run: staticcheck ./...

      - name: Build
        run: go build -o ./bin/ ./cmd/zabbix-maintenance-api
