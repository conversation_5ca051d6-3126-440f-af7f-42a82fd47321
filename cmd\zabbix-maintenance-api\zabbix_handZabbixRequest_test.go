package main

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestHandleZabbixRequestZabbixRequestValuesSet(t *testing.T) {
	_httpClient = newTestClient(func(req *http.Request) *http.Response {
		statusCode := 200
		message := `{ "message":"OK"}`

		var request zabbixRequest
		data, _ := io.ReadAll(req.Body)
		json.Unmarshal(data, &request)

		if request.ID == 0 && request.JSONRPC == "" {
			statusCode = 500
			message = "Missing ID and JSONRPC"
		} else if request.ID == 0 {
			statusCode = 500
			message = "Missing ID"
		} else if request.JSONRPC == "" {
			statusCode = 500
			message = "Missing JSONRPC"
		}

		return &http.Response{
			StatusCode: statusCode,
			Body:       io.NopCloser(bytes.NewBufferString(message)),
			Header:     make(http.Header),
		}
	})

	_, err := handleZabbixRequest(zabbixRequest{})

	assert.NoError(t, err)
}
