package main

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/StatusCakeDev/statuscake-go"
	"github.com/gorilla/mux"
)

type maintenceRequest struct {
	Identifier string
	UserAgent  string
	ClientIP   string
}

func writeOutput(message string) string {
	return fmt.Sprintf(`{ "Message":"%s"}`, message)
}

func readUserIP(r *http.Request) string {
	log.Trace().Msg("Entered function readUserIP")

	ipAddress := r.RemoteAddr
	slog := log.With().Str("RemoteAddress", r.RemoteAddr).Logger()

	slog.Trace().Msg("Read User IP: Checking if Remote Address on the HTTP request contains a port number")
	if strings.Contains(ipAddress, ":") {
		slog.Trace().Msg("Read User IP: Remote Address contains a port number, removing it to only have the IP")
		ipAddress = strings.Split(ipAddress, ":")[0]
		slog = slog.With().Str("IP", ipAddress).Logger()
		slog.Trace().Msg("Read User IP: Updated value of IP address")
	}

	log.Trace().Msg("Read User IP: Checking if HTTP request contains the HTTP header X-Real-IP")
	ip := r.Header.Get("X-Real-IP")
	if ip != "" {
		slog.Trace().Str("X-Real-IP", ip).Msg("Read User IP: Found X-Real-IP HTTP header, adding to ip")
		ipAddress = ipAddress + ", " + ip

		slog.Trace().Msg("Read User IP: Updated value of IP address")
	}

	log.Trace().Msg("Read User IP: Checking if HTTP request contains the HTTP header X-Forwarded-For")
	ip = r.Header.Get("X-Forwarded-For")
	if ip != "" {
		slog.Trace().Str("X-Forwarded-For", ip).Msg("Read User IP: Found X-Forwarded-For HTTP header, adding to ip")
		ipAddress = ipAddress + ", " + ip

		slog.Trace().Msg("Read User IP: Updated value of IP address")
	}

	slog.Trace().Msg("Read User IP: Returning value of remote users IP")
	return ipAddress
}

func showCacheEntries(w http.ResponseWriter, r *http.Request) {
	log.Trace().Msg("Entered function showCacheEntries")
	w.Header().Set("Content-Type", "application/json")

	slog := log.With().Str("User-Agent", r.Header.Get("user-agent")).Str("ClientIP", readUserIP(r)).Logger()

	slog.Info().Msg("Show Cache: Received request to show all cache entries")

	slog.Trace().Msg("Show Cache: Converting all cache entries to JSON")
	body, err := json.Marshal(_hostIDCache)
	if err != nil {
		slog.Error().Err(err).Msg("Show Cache: Failed to convert cache entries to JSON")
		http.Error(w, writeOutput("Internal server error"), http.StatusInternalServerError)
		return
	}

	w.Write(body)
}

func showMappings(w http.ResponseWriter, r *http.Request) {
	log.Trace().Msg("Entered function showMappings")
	w.Header().Set("Content-Type", "application/json")

	slog := log.With().Str("User-Agent", r.Header.Get("user-agent")).Str("ClientIP", readUserIP(r)).Logger()
	slog.Info().Msg("Show Mappings: Received request to show all mappings")

	slog.Trace().Msg("Show Mappings: Converting all mappings to JSON")
	body, err := json.Marshal(_mappings)
	if err != nil {
		slog.Error().Err(err).Msg("Show Mappings: Failed to convert mappings to JSON")
		http.Error(w, writeOutput("Internal server error"), http.StatusInternalServerError)
		return
	}

	w.Write(body)
}

func healthCheck(w http.ResponseWriter, r *http.Request) {
	log.Trace().Msg("Entered function healthCheck")
	w.Header().Set("Content-Type", "application/json")

	log.Trace().Msg("Health Check: Request auth token")
	_, err := getAuthToken()
	if err != nil {
		log.Trace().Err(err).Msg("Health Check: Failed to get auth token")
		http.Error(w, writeOutput("Unable to get auth token"), http.StatusInternalServerError)
		return
	}

	w.Write([]byte(`{"status":"healthy"}`))
}

func asyncCreateMaintenceWindowHandler(requests <-chan maintenceRequest) {
	log.Trace().Msg("Entered function asyncCreateMaintenceWindowHandler")

	log.Trace().Msg("Async Creation of Maintence Windows: Staring loops to handle request")
	for request := range requests {
		slog := log.With().Str("Identifier", request.Identifier).Str("ClientIP", request.ClientIP).Str("UserAgent", request.UserAgent).Logger()
		slog.Debug().Msg("Async Creation of Maintence Windows: Received maintence request via channel")

		name := fmt.Sprintf("%s - Deployment - %v", request.Identifier, time.Now().Unix())
		slog = slog.With().Str("MaintenanceWindowName", name).Logger()

		slog.Trace().Msg("Async Creation of Maintence Windows: Looking for identifier in mappings")
		identifier := request.Identifier
		s, ok := _mappings[identifier]

		if ok == false {
			slog.Debug().Msg("Async Creation of Maintence Windows: Identifier not found in mappings, assuming its a hostname")
			s = identifier
		}

		slog.Trace().Msg("Async Creation of Maintence Windows: Trying to cast identifer/mapping to string")
		v, ok := s.(string)
		if ok == false {
			slog.Error().Interface("Data", s).Msg("Async Creation of Maintence Windows: Failed to convert data to string")
			continue
		}

		servers := strings.Split(v, ",")

		slog.Trace().Msg("Async Creation of Maintence Windows: Request auth token")
		token, err := getAuthToken()
		if err != nil {
			if isBadStatusCodeError(err) {
				slog.Error().Err(err).Msg("Async Creation of Maintence Windows: Zabbix return bad status code when requesting auth token")
			} else {
				slog.Error().Err(err).Msg("Async Creation of Maintence Windows: Failed to get auth token from zabbix")
			}
			continue
		}
		slog.Trace().Msg("Async Creation of Maintence Windows: Got auth token")

		var hostIDs []int
		var notFound []string
		statusCakeFound := false
		// we update the cache of host IDs every hour by just
		// in case we should verify that all servers specified
		// via the identifier have a host ID
		slog.Trace().Msg("Async Creation of Maintence Windows: Checking we have host IDs for all servers")
		for _, server := range servers {
			slog := slog.With().Str("Server", server).Logger()
			// if a server name is statuscake it means the identifier is used as a
			// tag in statuscake and we should create a maintenance window in statuscake
			if strings.EqualFold(server, "statuscake") {
				slog.Debug().Msg("Async Creation of Maintence Windows: Found server named statuscake, this means we need to inform statuscake about the maintence")
				statusCakeFound = true
				continue
			}

			// When adding items from the zabbix api the name is always forced to lower
			server = strings.ToLower(server)
			if id, ok := _hostIDCache[server]; ok == false {
				slog.Debug().Msg("Async Creation of Maintence Windows: Host ID for server not found in cache")
				notFound = append(notFound, server)
			} else {
				slog.Trace().Msg("Async Creation of Maintence Windows: Host ID for server found in cache")
				hostIDs = append(hostIDs, id)
			}
		}

		if statusCakeFound {
			slog.Trace().Msg("Async Creation of Maintence Windows: Creating maintence window at statuscake")
			id, err := CreateStausCakeMaintenceWindow([]string{identifier}, name)

			if err != nil {
				if e, ok := err.(statuscake.APIError); ok {
					slog.Error().Err(e).Interface("errors", e.Errors).Interface("Parrent", e.Unwrap()).Msg("Async Creation of Maintence Windows: Failed to create statuscake maintenance window")
				} else {
					slog.Error().Err(err).Msg("Async Creation of Maintence Windows: Failed to create statuscake maintenance window")
				}
			} else {
				slog.Debug().Str("Statuscake ID", id).Msg("Async Creation of Maintence Windows: Created statuscake maintenance window")
			}
		}

		// if for some reason we are missing host IDs lets
		// get them from zabbix
		if len(notFound) > 0 {
			slog.Trace().Int("Count", len(notFound)).Msg("Async Creation of Maintence Windows: Some servers didn't have their host ID cache, requesting from zabbix")
			if ids, err := getHostIDs(token, notFound); err != nil {
				if isBadStatusCodeError(err) {
					slog.Error().Err(err).Msg("Async Creation of Maintence Windows: Zabbix return bad status code when requesting host ID")
				} else if isMissingServersError(err) {
					slog.Error().Msg("Async Creation of Maintence Windows: Amount of host IDs requsted don't match the amount of host IDs we got")
					continue
				} else {
					slog.Error().Err(err).Msg("Async Creation of Maintence Windows: Failed to get host IDs from zabbix")
				}
				continue
			} else {
				hostIDs = append(hostIDs, ids...)
			}
		}
		slog.Debug().Int("HostIDCount", len(hostIDs)).Int("ServerCount", len(servers)).Msg("Async Creation of Maintence Windows: Got all avaliable host ids for maintenance window")

		slog.Trace().Msg("Async Creation of Maintence Windows: Sending maintenance request to zabbix")
		ids, err := requestMaintenance(token, hostIDs, name)
		if err != nil {
			if isBadStatusCodeError(err) {
				slog.Error().Err(err).Msg("Async Creation of Maintence Windows: Zabbix return bad status code when requesting a maintenance window")
			} else if isDryRunError(err) {
				slog.Debug().Err(err).Msg("Async Creation of Maintence Windows: Maintenance window not created as we are running in dryrun mode")
			} else {
				slog.Error().Err(err).Msg("Async Creation of Maintence Windows: Failed to request a maintenace window from zabbix")
			}
			continue
		}

		slog.Info().Strs("Maintenance windows IDs", ids).Ints("Host IDs", hostIDs).Strs("Servers", servers).Msg("Async Creation of Maintence Windows: Maintenance window created")
	}
}

func asyncRemoveMaintenceWindowHandler(requests <-chan maintenceRequest) {
	log.Trace().Msg("Entered function asyncRemoveMaintenceWindowHandler")

	log.Trace().Msg("Async Removal of Maintence Windows: Staring loops to handle request")
	for request := range requests {
		slog := log.With().Str("Identifier", request.Identifier).Str("ClientIP", request.ClientIP).Str("UserAgent", request.UserAgent).Logger()
		slog.Debug().Msg("Async Removal of Maintence Windows: Received maintence removal request via channel")

		identifier := request.Identifier
		name := fmt.Sprintf("%s - deployment", identifier)
		name = strings.ToLower(name)
		slog = slog.With().Str("Maintenance Window Name", name).Logger()

		slog.Trace().Msg("Async Removal of Maintence Windows: Requesting auth token")
		token, err := getAuthToken()
		if err != nil {
			if isBadStatusCodeError(err) {
				slog.Error().Err(err).Msg("Async Removal of Maintence Windows: Zabbix return bad status code when requesting auth token")
			} else {
				slog.Error().Err(err).Msg("Async Removal of Maintence Windows: Failed to get auth token from zabbix")
			}
			continue
		}

		slog.Trace().Msg("Async Removal of Maintence Windows: Requesting a list of all maintenance windows")
		windows, err := getAllMaintenanceWindows(token)
		if err != nil {
			if isBadStatusCodeError(err) {
				slog.Error().Err(err).Msg("Async Removal of Maintence Windows: Zabbix return bad status code when requesting all maintenance windows")
			} else {
				slog.Error().Err(err).Msg("Async Removal of Maintence Windows: Failed to get all maintenance windows from zabbix")
			}
			continue
		}

		slog.Trace().Msg("Async Removal of Maintence Windows: Looking for identifier in mappings")
		s, ok := _mappings[identifier]

		if ok == false {
			slog.Debug().Msg("Async Removal of Maintence Windows: Identifier not found in mappings, assuming its a hostname")
			s = identifier
		}

		slog.Trace().Msg("Async Removal of Maintence Windows: Trying to cast identifer/mapping to string")
		v, ok := s.(string)
		if ok == false {
			slog.Error().Interface("Data", s).Msg("Async Removal of Maintence Windows: Failed to convert data to string")
			continue
		}

		servers := strings.Split(v, ",")

		slog.Trace().Msg("Async Removal of Maintence Windows: Checking if we need to remove the maintence windows from statuscake")
		for _, server := range servers {
			slog := slog.With().Str("Server", server).Logger()

			if strings.EqualFold(server, "statuscake") {
				slog.Trace().Msg("Async Removal of Maintence Windows: Found server named statuscake, requesting list of maintence windows from statuscake")
				maintWindows, err := ListStausCakeMaintenceWindow()

				if err != nil {
					slog.Error().Err(err).Msg("Async Removal of Maintence Windows: Failed to get list of maintenance windows from statuscake")
					break
				}

				slog.Trace().Msg("Async Removal of Maintence Windows: Checking all maintence windows from statuscake to see if we need to remove one of them")
				for _, maintWindow := range maintWindows {
					slog := slog.With().Str("Maintence window name", maintWindow.Name).Logger()

					n := strings.ToLower(maintWindow.Name)
					if strings.HasPrefix(n, name) == false {
						slog.Trace().Msg("Async Removal of Maintence Windows: Skipping maintenance window as it don't match what we want to remove")
						continue
					} else {
						slog.Trace().Msg("Async Removal of Maintence Windows: Found matching maintenance window, requesting deletion from statuscake")
						err := DeleteStausCakeMaintenceWindow(maintWindow.ID)
						if err != nil {
							slog.Error().Err(err).Msg("Async Removal of Maintence Windows: Error when deleting statuscake maintenance window")
						}
					}
				}
			}
		}

		var maintIDs []int
		slog.Trace().Msg("Async Removal of Maintence Windows: Checking all zabbix maintenance windows for any to remove")
		for _, window := range windows {
			slog := slog.With().Int("MaintenanceWindowID", window.ID).Str("MaintenceWindowName", window.name).Logger()
			n := strings.ToLower(window.name)
			// if the name don't start with `{identifier} - deployment` its not one
			// we have created and we should not delete it.
			if strings.HasPrefix(n, name) == false {
				slog.Trace().Msg("Async Removal of Maintence Windows: Skipping maintenance window as it don't match what we want to remove")
				continue
			}

			// check if the maintenance window contains a host that match one of the
			// host from the identifier specified. This is only to make sure we havn't
			// made a mistake as the name check should make sure we have the correct
			// maintenance window as that check is based on the identifier specifed
			// by the client
			slog.Trace().Msg("Async Removal of Maintence Windows: Found matching maintenance window, checking it contains one of the expected servers")
			for _, host := range window.hosts {
				slog := slog.With().Str("Host", host.host).Logger()
				for _, server := range servers {
					slog := slog.With().Str("Server", server).Logger()
					if strings.EqualFold(server, host.host) == true {
						slog.Trace().Msg("Async Removal of Maintence Windows: Found host that match server name in maintenance window")
						maintIDs = append(maintIDs, window.ID)
						slog.Debug().Msg("Async Removal of Maintence Windows: Maintenance window added to list of maintenance windows to delete")
						break
					}
				}
			}
		}

		if len(maintIDs) == 0 {
			slog.Debug().Msg("Async Removal of Maintence Windows: No maintenance windows found")
			continue
		}

		slog.Trace().Int("Count", len(maintIDs)).Msg("Async Removal of Maintence Windows: Requesting deletion of maintenance windows")
		ids, err := deleteMaintenanceWindow(token, maintIDs)
		if err != nil {
			if isBadStatusCodeError(err) {
				slog.Error().Err(err).Msg("Async Removal of Maintence Windows: Zabbix return bad status code when deleting maintenace windows")
			} else if isDryRunError(err) {
				slog.Debug().Err(err).Msg("Async Removal of Maintence Windows: Maintenance window not removed as we are running in dryrun mode")
			} else {
				slog.Error().Err(err).Msg("Async Removal of Maintence Windows: Failed to delete maintenance windows from zabbix")
			}
			continue
		}

		slog.Info().Strs("MaintenaceWindowIDs", ids).Strs("Servers", servers).Msg("Async Removal of Maintence Windows: Removed maintenance windows")
	}

}

func removeMaintenanceWindowRequest(w http.ResponseWriter, r *http.Request) {
	log.Trace().Msg("Entered function removeMaintenanceWindowRequest")
	w.Header().Set("Content-Type", "application/json")

	vars := mux.Vars(r)
	identifier, ok := vars["identifier"]
	userAgent := r.Header.Get("user-agent")
	clientIP := readUserIP(r)

	slog := log.With().Str("User-Agent", userAgent).Str("ClientIP", clientIP).Str("Identifier", identifier).Logger()

	slog.Debug().Msg("Remove Maintenance Window: Receive HTTP request to remove a maintenance window")

	if ok == false || len(identifier) == 0 {
		slog.Warn().Str("Path", r.URL.Path).Interface("Vars", vars).Msg("Remove Maintenance Window: Unable to process request, missing identifier")
		http.Error(w, writeOutput("Missing identifier, unable to set servers in maintenance"), http.StatusBadRequest)
		return
	}

	maintRequest := maintenceRequest{
		Identifier: identifier,
		UserAgent:  userAgent,
		ClientIP:   clientIP,
	}

	if _deletionChannel == nil {
		slog.Error().Msg("Remove Maintenance Window: Unable to sent deletion request as deletion channel not initialized")
		http.Error(w, writeOutput("Internal server error"), http.StatusInternalServerError)
		return
	}

	slog.Trace().Msg("Remove Maintenance Window: Sending deletion request to deletion channel")
	_deletionChannel <- maintRequest
	slog.Info().Msg("Remove Maintenance Window: Deletion request sent to deletion channel")
}

func createMaintenanceWindowRequest(w http.ResponseWriter, r *http.Request) {
	log.Trace().Msg("Entered function createMaintenanceWindowRequest")
	w.Header().Set("Content-Type", "application/json")

	vars := mux.Vars(r)
	identifier, ok := vars["identifier"]
	userAgent := r.Header.Get("user-agent")
	clientIP := readUserIP(r)

	slog := log.With().Str("User-Agent", userAgent).Str("ClientIP", clientIP).Str("Identifier", identifier).Logger()

	slog.Debug().Msg("Create Maintenance Window: Receive HTTP request to create a maintenance window")

	if ok == false || len(identifier) == 0 {
		slog.Warn().Str("Path", r.URL.Path).Interface("Vars", vars).Msg("Create Maintenance Window: Unable to process request, missing identifier")
		http.Error(w, writeOutput("Missing identifier, unable to set servers in maintenance"), http.StatusBadRequest)
		return
	}

	maintRequest := maintenceRequest{
		Identifier: identifier,
		UserAgent:  userAgent,
		ClientIP:   clientIP,
	}

	if _creationChannel == nil {
		slog.Error().Msg("Create Maintenance Window: Unable to sent creation request as creation channel not initialized")
		http.Error(w, writeOutput("Internal server error"), http.StatusInternalServerError)
		return
	}

	slog.Trace().Msg("Create Maintenance Window: Sending creation request to creation channel")
	_creationChannel <- maintRequest
	slog.Info().Msg("Create Maintenance Window: Creation request sent to creation channel")
}
