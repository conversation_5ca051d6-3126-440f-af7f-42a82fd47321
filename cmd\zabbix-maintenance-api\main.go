package main

import (
	"flag"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/gorilla/mux"
	"gopkg.in/yaml.v3"
)

var (
	_dryRun          bool
	_mappingFile     string
	_mappings        map[string]interface{}
	_hostIDCache     map[string]int
	_listenIP        string
	_listenPort      string
	_deletionChannel chan maintenceRequest
	_creationChannel chan maintenceRequest
	_cfg             = config{
		ZabbixSchema: "https", // set to make testing simpler
	}
)

func initFlags() {
	var (
		cfgPath   string
		l         string
		lf        string
		cliL      string
		fileL     string
		debuglog  bool
		tracelog  bool
		debugonly bool
		traceonly bool
		lma       int
		lmb       int
		lms       int
	)

	// dryrun is true be default until testing is done
	flag.BoolVar(&_dryRun, "dryrun", false, "Don't update data")
	flag.StringVar(&cfgPath, "cfg", "", "path for config file")
	flag.StringVar(&l, "loglevel", "INFO", "Loglevel to user, trace, debug, info, warn, error, fatal, panic")
	flag.StringVar(&lf, "logfile", "", "Write output to logfile")
	flag.StringVar(&_mappingFile, "mappingfile", "mappings.yaml", "Path to yaml file contaning mappings")
	flag.StringVar(&_listenIP, "listenIP", "0.0.0.0", "IP to listen for request on")
	flag.StringVar(&_listenPort, "listenPort", "8080", "port to listen for request on")

	flag.StringVar(&cliL, "cliloglevel", "INFO", "Loglevel to use for CLI, trace, debug, info, warn, error, fatal, panic")
	flag.StringVar(&fileL, "fileloglevel", "INFO", "Loglevel to use for CLI, trace, debug, info, warn, error, fatal, panic")
	flag.BoolVar(&debuglog, "debuglog", false, "Create logfile for debug output and above")
	flag.BoolVar(&tracelog, "tracelog", false, "Create logfile for trace output and above")
	flag.BoolVar(&debugonly, "debugonly", false, "Only output debug messages to debug log")
	flag.BoolVar(&traceonly, "traceonly", false, "Only output trace messages to debug log")
	flag.IntVar(&lma, "logmaxage", 3, "How many days of logs to keep")
	flag.IntVar(&lms, "logmaxsize", 200, "How many megabytes to allow the log to grow to")
	flag.IntVar(&lmb, "logmaxbackup", 5, "How many backup of logs to keep")

	flag.Parse()

	logger(LogConfig{
		CLILogLevel:   cliL,
		DebugLog:      debuglog,
		TraceLog:      tracelog,
		FileLogLevel:  fileL,
		Filename:      lf,
		DebugOnly:     debugonly,
		TraceOnly:     traceonly,
		LogMaxAge:     lma,
		LogMaxSize:    lms,
		LogMaxBackups: lmb,
	})

	err := _cfg.load(cfgPath)
	log.Trace().Msg("Config loaded")
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to load config file")
	}
}

func main() {
	initFlags()

	if _cfg.isValid() == false {
		log.Fatal().Msg("Main: Config data is not valid")
	}

	log.Trace().Msg("Main: Reading mapping file")
	if s, e := os.ReadFile(_mappingFile); e == nil {
		e := yaml.Unmarshal(s, &_mappings)
		if e != nil {
			log.Fatal().Err(e).Msg("Main: Failed to parse mapping file")
		}
	} else {
		log.Fatal().Err(e).Msg("Main: Failed to read mapping file")
	}

	/*
		We don't expect to have many requests at the same time, but some will happen
		so we haev a buffer. 16 was picked as it should be high enough that we don't
		hit that amount.
	*/
	_deletionChannel = make(chan maintenceRequest, 16)
	_creationChannel = make(chan maintenceRequest, 16)

	go updateHostIDCache()
	go asyncRemoveMaintenceWindowHandler(_deletionChannel)
	go asyncCreateMaintenceWindowHandler(_creationChannel)

	myRouter := mux.NewRouter()

	myRouter.HandleFunc("/{identifier}/add", createMaintenanceWindowRequest)
	myRouter.HandleFunc("/{identifier}/create", createMaintenanceWindowRequest)

	myRouter.HandleFunc("/{identifier}/delete", removeMaintenanceWindowRequest)
	myRouter.HandleFunc("/{identifier}/remove", removeMaintenanceWindowRequest)

	myRouter.HandleFunc("/mappings", showMappings)
	myRouter.HandleFunc("/identifiers", showMappings)
	myRouter.HandleFunc("/health", healthCheck)
	myRouter.HandleFunc("/list/mappings", showMappings)
	myRouter.HandleFunc("/list/identifiers", showMappings)
	myRouter.HandleFunc("/list/cache", showCacheEntries)

	log.Info().Msgf("Main: Starting http server on %s:%s", _listenIP, _listenPort)
	if err := http.ListenAndServe(fmt.Sprintf("%s:%s", _listenIP, _listenPort), myRouter); err != nil {
		log.Fatal().Err(err).Msg("Main: HTTP server failed to start")
	}
}

func updateHostIDCache() {
	log.Trace().Msg("Entered function updateHostIDCache")
	errors := 0
	log.Trace().Msg("Update Host ID Cache: Start while loop to update host ID cache")
	for {
		// if we already have a cache lets wait 1 hour before
		// updating it
		if _hostIDCache != nil {
			log.Trace().Msg("Update Host ID Cache: Host ID cache already avaliable, waiting one hour before updating")
			time.Sleep(1 * time.Hour)
		} else if errors > 0 {
			d, _ := time.ParseDuration(fmt.Sprintf("%ds", 10*errors))
			log.Trace().Msgf("Update Host ID Cache: Host ID cache not initialized but received errors backing of for %s", d.String())
			time.Sleep(d)
		}

		log.Debug().Msg("Update Host ID Cache: Updating host ID cache")

		log.Trace().Msg("Update Host ID Cache: Requesting auth token")
		token, err := getAuthToken()
		if err != nil {
			errors++
			if isBadStatusCodeError(err) {
				log.Error().Err(err).Msg("Update Host ID Cache: Zabbix return bad status code")
			} else if strings.Contains(err.Error(), "connection refused") {
				log.Error().Err(err).Msg("Update Host ID Cache: Zabbix refused connectiong when getting auth token, retry in 10 seconds")
				time.Sleep(10 * time.Second)
			} else {
				log.Error().Err(err).Msg("Update Host ID Cache: Failed to get auth token from zabbix")
			}
			continue
		}

		log.Trace().Msg("Update Host ID Cache: Requesting all host IDs from zabbix")
		ids, err := getAllHostIDs(token)
		if err != nil {
			errors++
			if isBadStatusCodeError(err) {
				log.Error().Err(err).Msg("Update Host ID Cache: Zabbix return bad status code")
			} else {
				log.Error().Err(err).Msg("Update Host ID Cache: Failed to get host IDs from zabbix")
			}
			continue
		}

		// if we don't have any cache just use the output directly
		// if we do have a cache we need to see if any new servers
		// have been added to zabbix and add them.
		if _hostIDCache == nil {
			log.Debug().Msg("Update Host ID Cache: Empty host id cache overriding with output from getAllHostIDs")
			_hostIDCache = ids
		} else {
			log.Trace().Msg("Update Host ID Cache: Looping though all host IDs to add any we are missing")
			for key, val := range ids {
				key = strings.ToLower(key)
				slog := log.With().Str("Host", key).Int("ID", val).Logger()
				v, ok := _hostIDCache[key]
				if ok == false {
					_hostIDCache[key] = val
					slog.Trace().Msg("Update Host ID Cache: Found new host for host ID cache")
				} else if val != v {
					// Zabbix is our source of truth for this data
					// so we should believe its data, but the host
					// ID should not change.
					_hostIDCache[key] = val
					log.Warn().Msg("Update Host ID Cache: Host ID from zabbix don't match the data in our cache. Using value from zabbix")
				}
			}
		}
		log.Trace().Msg("Update Host ID Cache: Host IDs updated, resetting error counter")
		errors = 0
	}
}
