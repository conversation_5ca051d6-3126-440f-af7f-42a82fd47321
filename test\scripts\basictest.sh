#!/usr/bin/env bash

curl -Ss -H "X-Real-IP: *******" -H "X-Forwarded-For: *******" http://127.0.0.1:8080/health | jq '.' > /dev/null
curl -Ss -H "X-Real-IP: *******" -H "X-Forwarded-For: *******" http://127.0.0.1:8080/mappings | jq '.' > /dev/null
curl -Ss -H "X-Real-IP: *******" -H "X-Forwarded-For: *******" http://127.0.0.1:8080/list/cache | jq '.' > /dev/null
curl -Ss -H "X-Real-IP: *******" -H "X-Forwarded-For: *******" http://127.0.0.1:8080/rs-sandbox/add | jq '.' > /dev/null
curl -Ss -H "X-Real-IP: *******" -H "X-Forwarded-For: *******" http://127.0.0.1:8080/rs-sandbox/remove | jq '.' > /dev/null
curl -Ss -H "X-Real-IP: *******" -H "X-Forwarded-For: *******" http://127.0.0.1:8080/CORP-dc01.spcph.local/add | jq '.' > /dev/null
curl -Ss -H "X-Real-IP: *******" -H "X-Forwarded-For: *******" http://127.0.0.1:8080/CORP-dc01.spcph.local/remove | jq '.' > /dev/null
