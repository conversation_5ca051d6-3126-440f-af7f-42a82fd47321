package main

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"strconv"
	"strings"
	"sync"
	"time"
)

var _zabbixRequestID = 1

// used by getAuthToken
var _authTokenLock sync.Mutex
var _authToken string

type zabbixRequest struct {
	JSONRPC string      `json:"jsonrpc"`
	Method  string      `json:"method"`
	Params  interface{} `json:"params"`
	ID      int         `json:"id"`
	Auth    string      `json:"auth,omitempty"`
}

type zabbixError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    string `json:"data"`
}

type zabbixResponse struct {
	JSONRPC string      `json:"jsonrpc"`
	ID      int         `json:"id"`
	Result  interface{} `json:"result"`
	Error   zabbixError `json:"error"`
}
type timePeriod struct {
	ID     string `json:"timeperiodid,omitempty"`
	Period int    `json:"period"`
}

type maintenanceWindow struct {
	ID              int
	name            string
	maintenanceType int
	description     string
	hosts           []zabbixHost
	activeSince     time.Time
	activeUntill    time.Time
}

type zabbixHost struct {
	ID   int `json:"hostid"`
	host string
}

func isTokenValid(token string) bool {
	log.Trace().Msg("Entered function isTokenValid")

	request := zabbixRequest{
		Method: "user.checkAuthentication",
		Params: map[string]interface{}{
			"sessionid": token,
		},
	}

	log.Trace().Msg("Valid Token: Sending request to zabbix to check if token is valid")
	response, err := handleZabbixRequest(request)
	if err != nil {
		log.Trace().Err(err).Msg("Valid Token: Received error from zabbix, assuming token is invalid")
		return false
	}
	result, ok := response.Result.(map[string]interface{})
	if ok == false {
		log.Trace().Err(err).Msg("Valid Token: Unable to cast zabbix result, assuming token is invalid")
		return false
	}

	sessionid, ok := result["sessionid"].(string)
	if ok == false {
		log.Trace().Err(err).Msg("Valid Token: Unable to cast sessionid to string, assuming token is invalid")
		return false
	}

	return sessionid == token
}

func getAuthToken() (string, error) {
	log.Trace().Msg("Entered function getAuthToken")

	log.Trace().Msg("Get Auth Token: Requesting auth token lock")
	_authTokenLock.Lock()
	defer _authTokenLock.Unlock()
	defer log.Trace().Msg("Get Auth Token: Releasing auth token lock")

	log.Trace().Msg("Get Auth Token: Got auth token lock")

	log.Trace().Msg("Get Auth Token: Checking if current token is valid")
	if _authToken != "" && isTokenValid(_authToken) == true {
		log.Debug().Msg("Get Auth Token: Token is valid, reusing")
		return _authToken, nil
	}

	request := zabbixRequest{
		Method: "user.login",
		Params: map[string]interface{}{
			"username": _cfg.ZabbixUser,
			"password": _cfg.ZabbixPassword,
		},
	}

	log.Trace().Msg("Get Auth Token: Sending request to zabbix")
	response, err := handleZabbixRequest(request)
	if err != nil {
		log.Trace().Err(err).Msg("Get Auth Token: Got error from zabbix")
		return "", err
	}

	log.Trace().Msg("Get Auth Token: Trying to cast response result to string")
	s, ok := response.Result.(string)
	if ok == true {
		log.Trace().Msg("Get Auth Token: Got a auth token from zabbix")
		_authToken = s
		return _authToken, nil
	}

	return "", zabbixError{Message: fmt.Sprintf("Type of result contaning token was %T not string", response.Result), Code: 12345}
}

func getAllHostIDs(token string) (map[string]int, error) {
	log.Trace().Msg("Entered function getAllHostIDs")
	hostMappings := make(map[string]int)

	request := zabbixRequest{
		Method: "host.get",
		Params: map[string]interface{}{},
		Auth:   token,
	}
	log.Trace().Msg("Get All Host IDs: Requesting list of all host from zabbix")
	response, err := handleZabbixRequest(request)

	if err != nil {
		log.Trace().Err(err).Msg("Get All Host IDs: Failed to get list of all host from zabbix")
		return hostMappings, err
	}

	log.Trace().Msg("Get All Host IDs: Casting zabbix response to []interface{}")
	hosts, ok := response.Result.([]interface{})
	if ok == false {
		log.Trace().Msg("Get All Host IDs: Failed to cast zabbix response to []interface{}")
		return hostMappings, zabbixError{Code: 12345, Message: fmt.Sprintf("Type of result contaning token was %T not []interface{}", response.Result)}
	}

	log.Trace().Msg("Get All Host IDs: Starting process for extracting all host IDs from zabbix response")
	hostMappings = extractHostIDs(hosts)
	log.Trace().Int("Count", len(hostMappings)).Msg("Get All Host IDs: Got all host IDs in the zabbix response")
	return hostMappings, nil
}

func getHostIDs(token string, servers []string) ([]int, error) {
	log.Trace().Msg("Entered function getHostIDs")
	var hostIDs []int

	log.Trace().Msg("Get Host IDs: Starting process for getting a list of all host IDs")
	hostMappings, err := getAllHostIDs(token)

	if err != nil {
		log.Trace().Err(err).Msg("Get Host IDs: Failed to get list of all host IDs")
		return hostIDs, err
	}

	var missingServers []string
	log.Trace().Msg("Get Host IDs: Looping though all servers to find their host IDs")
	for _, server := range servers {
		slog := log.With().Str("Server", server).Logger()
		if id, ok := hostMappings[server]; ok == false {
			missingServers = append(missingServers, server)
			slog.Warn().Msg("Get Host IDs: Unable to find host ID for server")
		} else {
			slog.Trace().Msg("Get Host IDs: Found host ID for server")
			hostIDs = append(hostIDs, id)
		}
	}

	if len(missingServers) > 0 {
		log.Trace().Int("Missing", len(missingServers)).Msg("Get Host IDs: A host ID was not found for all servers")
		return hostIDs, errMissingServers{Servers: missingServers}
	}

	log.Trace().Msg("Get Host IDs: Found host IDs for all servers")
	return hostIDs, nil
}

func extractHostIDs(hosts []interface{}) map[string]int {
	log.Trace().Msg("Entered function extractHostIDs")
	hostIDs := make(map[string]int)

	log.Trace().Msg("Extract Host IDs: Loop though all hosts to extract the ID")
	for _, host := range hosts {
		log.Trace().Msg("Extract Host IDs: Trying to cast host to map[string]interface{}")
		if data, ok := host.(map[string]interface{}); ok == true {
			log.Trace().Msg("Extract Host IDs: Host succesfully cast to map[string]interface{}")
			log.Trace().Msg("Extract Host IDs: Starting process to extract the name for the host")
			name, err := extractString(data, "name")
			if err != nil {
				log.Debug().Interface("Data", data).Err(err).Msg("Extract Host IDs: Unable to extract name")
				continue
			}

			log.Trace().Msg("Extract Host IDs: Starting process to extract the hostid for the host")
			id, err := extractInt(data, "hostid")
			if err != nil {
				log.Debug().Interface("Data", data).Err(err).Msg("Extract Host IDs: Unable to extract hostid")
				continue
			}

			log.Trace().Str("Name", name).Int("ID", id).Msg("Extract Host IDs: Adding name and ID to list")
			hostIDs[name] = id
		} else {
			log.Debug().Interface("Data", host).Msg("Extract Host IDs: Unable to cast host to map[string]interface{}")
		}
	}

	return hostIDs
}

func requestMaintenance(token string, hostIDs []int, name string) ([]string, error) {
	log.Trace().Msg("Entered function requestMaintenance")
	slog := log.With().Ints("HostIDs", hostIDs).Str("Name", name).Logger()

	var output []string

	var hosts []zabbixHost
	for _, id := range hostIDs {
		hosts = append(hosts, zabbixHost{ID: id})
	}

	slog.Trace().Msg("Request Zabbix Maintenance Window: Crafting zabbix request")
	request := zabbixRequest{
		Method: "maintenance.create",
		Params: map[string]interface{}{
			"name":         name,
			"hosts":        hosts,
			"active_since": time.Now().Unix(),
			"active_till":  time.Now().AddDate(0, 0, 1).Unix(),
			"timeperiods": []timePeriod{
				timePeriod{
					Period: _cfg.ZabbixDowntime,
				},
			},
		},
		Auth: token,
	}

	if _dryRun {
		slog.Trace().Msg("Request Zabbix Maintenance Window: Running in dryrun mode, skipping creation of maintenance window")
		return output, errDryRun{ZabbixMethod: request.Method}
	}

	slog.Trace().Msg("Request Zabbix Maintenance Window: Starting process of sending request to zabbix")
	response, err := handleZabbixRequest(request)
	if err != nil {
		slog.Trace().Err(err).Msg("Request Zabbix Maintenance Window: Failed to sent request to zabbix")
		return output, err
	}

	slog.Trace().Msg("Request Zabbix Maintenance Window: Casting zabbix response result to map[string]interface{}")
	result, ok := response.Result.(map[string]interface{})
	if ok == false {
		slog.Trace().Msg("Request Zabbix Maintenance Window: Failed to cast zabbix response result to map[string]interface{}")
		return output, zabbixError{Code: 12345, Message: fmt.Sprintf("Type of result contaning maintenance window id was %T not map[string]interface{}", response.Result)}
	}

	slog.Trace().Msg("Request Zabbix Maintenance Window: Getting maintenanceids object from zabbix response")
	m := result["maintenanceids"]
	slog.Trace().Msg("Request Zabbix Maintenance Window: Casting maintenanceids object to []interface{}")
	ids, ok := m.([]interface{})
	if ok == false {
		slog.Trace().Msg("Request Zabbix Maintenance Window: Failed to cast maintenanceids object to []interface{}")
		return output, zabbixError{Code: 12345, Message: fmt.Sprintf("Type of result contaning maintenance window id was %T not []interface{}", m)}
	}

	slog.Trace().Msg("Request Zabbix Maintenance Window: Loop though all maintenance IDs")
	for _, id := range ids {
		slog := slog.With().Interface("ID", id).Logger()
		switch v := id.(type) {
		case float64:
			slog.Trace().Msg("Request Zabbix Maintenance Window: Adding maintenance ID of the type float64 to output list as a string")
			output = append(output, fmt.Sprintf("%v", v))
		case int:
			slog.Trace().Msg("Request Zabbix Maintenance Window: Adding maintenance ID of the type int to output list as a string")
			output = append(output, fmt.Sprintf("%v", v))
		case string:
			slog.Trace().Msg("Request Zabbix Maintenance Window: Adding maintenance ID of the type string to output list")
			output = append(output, v)
		default:
			slog.Debug().Msgf("Request Zabbix Maintenance Window: ID not of an expected type: %T", id)
		}
	}

	slog.Trace().Strs("IDs", output).Msg("Request Zabbix Maintenance Window: Got maintenance window")

	return output, err
}

func deleteMaintenanceWindow(token string, maintenanceIDs []int) ([]string, error) {
	log.Trace().Msg("Entered function deleteMaintenanceWindow")
	slog := log.With().Ints("IDs", maintenanceIDs).Logger()

	var output []string

	slog.Trace().Msg("Delete Zabbix Maintenance Window: Crafting zabbix request")
	request := zabbixRequest{
		Method: "maintenance.delete",
		Params: maintenanceIDs,
		Auth:   token,
	}

	if _dryRun {
		slog.Trace().Msg("Delete Zabbix Maintenance Window: Running in dryrun mode, skipping creation of maintenance window")
		return output, errDryRun{ZabbixMethod: request.Method}
	}

	slog.Trace().Msg("Delete Zabbix Maintenance Window: Starting process of sending request to zabbix")
	response, err := handleZabbixRequest(request)
	if err != nil {
		slog.Trace().Err(err).Msg("Delete Zabbix Maintenance Window: Failed to sent request to zabbix")
		return output, err
	}

	slog.Trace().Msg("Delete Zabbix Maintenance Window: Casting zabbix response result to map[string]interface{}")
	result, ok := response.Result.(map[string]interface{})
	if ok == false {
		log.Trace().Msg("Delete Zabbix Maintenance Window: Failed to cast zabbix response result to map[string]interface{}")
		return output, zabbixError{Code: 12345, Message: fmt.Sprintf("Type of result contaning maintenance window id was %T not map[string]interface{}", response.Result)}
	}

	slog.Trace().Msg("Delete Zabbix Maintenance Window: Getting maintenanceids from result")
	m := result["maintenanceids"]

	slog.Trace().Msg("Delete Zabbix Maintenance Window: Casting maintenanceids object to []interface{}")
	ids, ok := m.([]interface{})
	if ok == false {
		slog.Trace().Msg("Delete Zabbix Maintenance Window: Failed to cast maintenanceids object to []interface{}")
		return output, zabbixError{Code: 12345, Message: fmt.Sprintf("Type of result contaning maintenance window id was %T not []interface{}", m)}
	}

	slog.Trace().Msg("Delete Zabbix Maintenance Window: Loop though all maintenance IDs")
	for _, id := range ids {
		slog := slog.With().Interface("ID", id).Logger()
		switch v := id.(type) {
		case float64:
			slog.Trace().Msg("Delete Zabbix Maintenance Window: Adding maintenance ID of the type float64 to output list as a string")
			output = append(output, fmt.Sprintf("%v", v))
		case int:
			slog.Trace().Msg("Delete Zabbix Maintenance Window: Adding maintenance ID of the type int to output list as a string")
			output = append(output, fmt.Sprintf("%v", v))
		case string:
			slog.Trace().Msg("Delete Zabbix Maintenance Window: Adding maintenance ID of the type string to output list")
			output = append(output, v)
		default:
			slog.Debug().Msgf("Delete Zabbix Maintenance Window: ID not of an expected type: %T", id)
		}
	}

	slog.Trace().Interface("data", result).Strs("IDs", output).Msg("Delete Zabbix Maintenance Window: Removed maintenance window")
	return output, err
}

func getAllMaintenanceWindows(token string) ([]maintenanceWindow, error) {
	log.Trace().Msg("Entered function getAllMaintenanceWindows")
	var output []maintenanceWindow

	log.Trace().Msg("Get All Zabbix Maintenance Windows: Crafting zabbix request")
	request := zabbixRequest{
		Method: "maintenance.get",
		Params: map[string]interface{}{
			"output":      "extend",
			"selectHosts": "extend",
		},
		Auth: token,
	}

	log.Trace().Msg("Get All Zabbix Maintenance Windows: Starting process of sending request to zabbix")
	response, err := handleZabbixRequest(request)

	log.Trace().Msg("Get All Zabbix Maintenance Windows: Casting zabbix response result to []interface{}")
	result, ok := response.Result.([]interface{})
	if ok == false {
		log.Trace().Msg("Get All Zabbix Maintenance Windows: Failed to cast zabbix response result to []interface{}")
		return output, zabbixError{Code: 12345, Message: fmt.Sprintf("Type of result contaning token was %T not []interface{}", response.Result)}
	}
	log.Trace().Msg("Get All Zabbix Maintenance Windows: Starting process of extracting all maintenance windows from result object")
	output = extractMaintenanceWindows(result)

	log.Trace().Int("Count", len(output)).Msgf("Get All Zabbix Maintenance Windows: Got %d maintenance windows", len(output))
	return output, err
}

func extractMaintenanceWindows(windows []interface{}) []maintenanceWindow {
	log.Trace().Msg("Entered function extractMaintenanceWindows")
	var output []maintenanceWindow

	log.Trace().Msg("Extract Maintenance Windows: Loop though all maintenance window objects")
	for _, window := range windows {
		log.Trace().Msg("Extract Maintenance Windows: Casting maintenance window object to map[string]interface{}")
		if data, ok := window.(map[string]interface{}); ok == true {
			var mw maintenanceWindow

			log.Trace().Msg("Extract Maintenance Windows: Extracting name from maintenance window object")
			name, err := extractString(data, "name")
			if err != nil {
				log.Debug().Interface("Data", data).Err(err).Msg("Extract Maintenance Windows: Unable to extract name")
				continue
			}
			mw.name = name

			log.Trace().Msg("Extract Maintenance Windows: Extracting maintenanceid from maintenance window object")
			id, err := extractInt(data, "maintenanceid")
			if err != nil {
				log.Debug().Interface("Data", data).Err(err).Msg("Extract Maintenance Windows: Unable to extract maintenanceid")
				continue
			}
			mw.ID = id

			log.Trace().Msg("Extract Maintenance Windows: Extracting maintenance_type from maintenance window object")
			mtype, err := extractInt(data, "maintenance_type")
			if err != nil {
				log.Debug().Interface("Data", data).Err(err).Msg("Extract Maintenance Windows: Unable to extract maintenance_type")
				continue
			}
			mw.maintenanceType = mtype

			log.Trace().Msg("Extract Maintenance Windows: Extracting description from maintenance window object")
			desc, err := extractString(data, "description")
			if err != nil {
				log.Debug().Interface("Data", data).Err(err).Msg("Extract Maintenance Windows: Unable to extract description")
				continue
			}
			mw.description = desc

			log.Trace().Msg("Extract Maintenance Windows: Extracting active_since from maintenance window object")
			since, err := extractInt64(data, "active_since")
			if err != nil {
				log.Debug().Interface("Data", data).Err(err).Msg("Extract Maintenance Windows: Unable to extract active_since")
				continue
			}
			log.Trace().Msg("Extract Maintenance Windows: Converting active_since to timestamp")
			mw.activeSince = time.Unix(since, 0)

			log.Trace().Msg("Extract Maintenance Windows: Extracting active_till from maintenance window object")
			untill, err := extractInt64(data, "active_till")
			if err != nil {
				log.Debug().Interface("Data", data).Err(err).Msg("Extract Maintenance Windows: Unable to extract active_till")
				continue
			}
			log.Trace().Msg("Extract Maintenance Windows: Converting active_till to timestamp")
			mw.activeUntill = time.Unix(untill, 0)

			log.Trace().Msg("Extract Maintenance Windows: Requesting hosts from maintenance window object")
			h, ok := data["hosts"]
			if ok == false {
				log.Debug().Interface("Data", data).Err(err).Msg("Extract Maintenance Windows: Unable to get hosts from maintenance window object")
				continue
			}

			log.Trace().Msg("Extract Maintenance Windows: Casting hosts object to []interface")
			hosts, ok := h.([]interface{})
			if ok == false {
				log.Debug().Interface("Data", data).Err(err).Msg("Extract Maintenance Windows: Unable to cast hosts object to []interface{}")
				continue
			}

			log.Trace().Msg("Extract Maintenance Windows: Extracting hosts from hosts object")
			mw.hosts = extractHosts(hosts)

			output = append(output, mw)
			log.Trace().Interface("MaintenanceWindow", mw).Msg("Extract Maintenance Windows: Added maintenance window to list")
		} else {
			log.Debug().Interface("Data", data).Msg("Extract Maintenance Windows: Failed to cast maintenance object to map[string]interface{}")
		}
	}

	return output
}

func extractHosts(hosts []interface{}) []zabbixHost {
	log.Trace().Msg("Entered function extractHosts")
	var output []zabbixHost

	log.Trace().Msg("Extract Hosts: Looping though host objects")
	for _, host := range hosts {
		log.Trace().Msg("Extract Hosts: Casting host object to map[string]interface{}")
		if data, ok := host.(map[string]interface{}); ok == true {
			var zh zabbixHost

			log.Trace().Msg("Extract Hosts: Extracting host from host object")
			name, err := extractString(data, "host")
			if err != nil {
				log.Debug().Interface("Data", data).Err(err).Msg("Extract Hosts: Unable to extract host")
				continue
			}
			zh.host = name

			log.Trace().Msg("Extract Hosts: Extracting hostid from host object")
			id, err := extractInt(data, "hostid")
			if err != nil {
				log.Debug().Interface("Data", data).Err(err).Msg("Extract Hosts: Unable to extract hostid")
				continue
			}
			zh.ID = id

			output = append(output, zh)
		} else {
			log.Debug().Interface("Data", host).Msg("Extract Hosts: Unable to cast host to map[string]interface{}")
		}
	}

	return output
}

func extractString(data map[string]interface{}, identifier string) (string, error) {
	log.Trace().Msg("Entered function extractString")
	slog := log.With().Str("Identifier", identifier).Logger()

	slog.Trace().Msg("Extract String: Checking data map for identifier")
	n, ok := data[identifier]
	if ok == false {
		slog.Trace().Msg("Extract String: Failed to find identifier in data map")
		return "", errors.New("unable to extract " + identifier)
	}

	slog = slog.With().Interface("data", n).Logger()

	slog.Trace().Msg("Extract String: Casting object to string")
	s, ok := n.(string)
	if ok == false {
		slog.Trace().Msg("Extract String: Failed to cast object to string")
		return "", errors.New("unable to convert identifier to string")
	}

	return s, nil
}

func extractInt64(data map[string]interface{}, identifier string) (int64, error) {
	log.Trace().Msg("Entered function extractInt64")
	slog := log.With().Str("Identifier", identifier).Logger()

	slog.Trace().Msg("Extract Int64: Checking data map for identifier")
	n, ok := data[identifier]
	if ok == false {
		slog.Trace().Msg("Extract Int64: Failed to find identifier in data map")
		return 0, errors.New("unable to extract " + identifier)
	}

	slog = slog.With().Interface("data", n).Logger()

	slog.Trace().Msg("Extract Int64: Casting object to string")
	s, ok := n.(string)
	if ok == false {
		slog.Trace().Msg("Extract Int64: Failed to cast object to string")
		return 0, errors.New("unable to convert value to string")
	}

	slog.Trace().Msg("Extract Int64: Converting string value to Int64")
	i, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		slog.Trace().Err(err).Msg("Extract Int64: Failed to convert string value to Int64")
		return 0, errors.New("unable to convert value to int")
	}

	return i, nil
}

func extractInt(data map[string]interface{}, identifier string) (int, error) {
	log.Trace().Msg("Entered function extractInt")
	slog := log.With().Str("Identifier", identifier).Logger()

	slog.Trace().Msg("Extract Int: Checking data map for identifier")
	n, ok := data[identifier]
	if ok == false {
		slog.Trace().Msg("Extract Int: Failed to find identifier in data map")
		return 0, errors.New("unable to extract " + identifier)
	}

	slog = slog.With().Interface("data", n).Logger()

	slog.Trace().Msg("Extract Int: Casting object to string")
	s, ok := n.(string)
	if ok == false {
		slog.Trace().Msg("Extract Int: Failed to cast object to string")
		return 0, errors.New("unable to convert value to string")
	}

	slog.Trace().Msg("Extract Int: Converting string value to int")
	i, err := strconv.Atoi(s)
	if err != nil {
		slog.Trace().Err(err).Msg("Extract Int: Failed to convert string value to int")
		return 0, errors.New("unable to convert value to int")
	}

	return i, nil
}

func handleZabbixRequest(request zabbixRequest) (zabbixResponse, error) {
	log.Trace().Msg("Entered function handleZabbixRequest")
	var response zabbixResponse
	url := fmt.Sprintf("%s://%s:%d/api_jsonrpc.php", _cfg.ZabbixSchema, _cfg.ZabbixHost, _cfg.ZabbixPort)

	if request.JSONRPC == "" {
		request.JSONRPC = "2.0"
		log.Trace().Msgf("Handle Zabbix Request: Setting JSONRPC value to %s on request", request.JSONRPC)
	}
	if request.ID == 0 {
		request.ID = _zabbixRequestID
		log.Trace().Msgf("Handle Zabbix Request: Setting request ID to %d on request", request.ID)
		_zabbixRequestID++
	}

	log.Trace().Msg("Handle Zabbix Request: Starting process of sending HTTP request to zabbix")
	stream, err := httpRequest(url, request, "POST")

	if err == nil {
		defer stream.Close()
	} else {
		log.Trace().Err(err).Msg("Handle Zabbix Request: Failed to sent HTTP request to zabbix")
		return response, err
	}
	log.Trace().Msg("Handle Zabbix Request: Got Response from zabbix")

	data, err := io.ReadAll(stream)
	if err != nil {
		log.Trace().Err(err).Msg("Handle Zabbix Request: Failed to read all the data from the zabbix response")
		return response, err
	}

	log.Trace().Msg("Handle Zabbix Request: Convert JSON response to zabbixResponse struct")
	err = json.Unmarshal(data, &response)
	if err != nil {
		log.Trace().Err(err).Msg("Handle Zabbix Request: Failed to convert JSON response")
		return response, err
	}

	if response.Error.Message != "" {
		log.Debug().Str("Message", response.Error.Message).Str("Data", response.Error.Data).Int("Code", response.Error.Code).Msg("Handle Zabbix Request: Got error from zabbix")
		return response, response.Error
	}

	return response, nil
}

func (e zabbixError) Error() string {
	return strings.TrimSpace(fmt.Sprintf("%s %s", e.Message, e.Data))
}

func isZabbixError(e error) bool {
	_, ok := e.(zabbixError)
	return ok
}

type errMissingServers struct {
	Servers []string
}

func (e errMissingServers) Error() string {
	return "The following servers was missing" + strings.Join(e.Servers, ", ")
}

func isMissingServersError(e error) bool {
	_, ok := e.(errMissingServers)
	return ok
}
